/**
 * 思维导图渲染视图
 * 基于D3.js实现思维导图的可视化渲染
 */

import * as d3 from 'd3';
import { MindMapNode, RenderConfig, ContextMenuItem } from './types';
import { MindMapEngine, MindMapChangeEvent } from './mindmap-engine';

export class MindMapRenderer {
  private container: HTMLElement;
  private svg: d3.Selection<SVGSVGElement, unknown, null, undefined>;
  private g: d3.Selection<SVGGElement, unknown, null, undefined>;
  private config: RenderConfig;
  private engine: MindMapEngine;
  private zoom: d3.ZoomBehavior<SVGSVGElement, unknown>;
  private nodeClickListeners: ((nodeId: string) => void)[] = [];
  private nodeDoubleClickListeners: ((nodeId: string) => void)[] = [];
  private contextMenuListeners: ((nodeId: string, event: MouseEvent) => void)[] = [];

  constructor(container: HTMLElement, engine: MindMapEngine, config?: Partial<RenderConfig>) {
    this.container = container;
    this.engine = engine;
    this.config = {
      width: 800,
      height: 600,
      nodeSpacing: 80,
      levelSpacing: 200,
      enableAnimation: true,
      animationDuration: 300,
      ...config
    };

    this.initializeSVG();
    this.setupZoom();
    this.bindEngineEvents();
  }

  /**
   * 初始化SVG容器
   */
  private initializeSVG(): void {
    // 清空容器
    d3.select(this.container).selectAll('*').remove();

    // 创建SVG
    this.svg = d3.select(this.container)
      .append('svg')
      .attr('width', this.config.width)
      .attr('height', this.config.height)
      .style('border', '1px solid #ccc')
      .style('background-color', 'var(--background-primary)');

    // 创建主要的g元素
    this.g = this.svg.append('g');

    // 添加样式
    this.addStyles();
  }

  /**
   * 添加CSS样式
   */
  private addStyles(): void {
    const style = document.createElement('style');
    style.textContent = `
      .mindmap-node {
        cursor: pointer;
        transition: all 0.2s ease;
      }
      
      .mindmap-node:hover {
        filter: brightness(1.1);
      }
      
      .mindmap-node-rect {
        fill: var(--background-secondary);
        stroke: var(--border-color);
        stroke-width: 1;
        rx: 5;
        ry: 5;
      }
      
      .mindmap-node-text {
        fill: var(--text-normal);
        font-family: var(--font-interface);
        font-size: 14px;
        text-anchor: middle;
        dominant-baseline: central;
        pointer-events: none;
      }
      
      .mindmap-node-heading-1 .mindmap-node-text {
        font-size: 20px;
        font-weight: bold;
      }
      
      .mindmap-node-heading-2 .mindmap-node-text {
        font-size: 18px;
        font-weight: bold;
      }
      
      .mindmap-node-heading-3 .mindmap-node-text {
        font-size: 16px;
        font-weight: bold;
      }
      
      .mindmap-node-ai-answer .mindmap-node-rect {
        stroke: var(--color-accent);
        stroke-width: 2;
      }
      
      .mindmap-link {
        fill: none;
        stroke: var(--border-color);
        stroke-width: 1.5;
      }
      
      .mindmap-node-selected .mindmap-node-rect {
        stroke: var(--color-accent);
        stroke-width: 3;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * 设置缩放和拖拽
   */
  private setupZoom(): void {
    this.zoom = d3.zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.1, 3])
      .on('zoom', (event) => {
        this.g.attr('transform', event.transform);
      });

    this.svg.call(this.zoom);
  }

  /**
   * 绑定引擎事件
   */
  private bindEngineEvents(): void {
    this.engine.addChangeListener((event: MindMapChangeEvent) => {
      this.handleEngineChange(event);
    });
  }

  /**
   * 处理引擎变更事件
   */
  private handleEngineChange(event: MindMapChangeEvent): void {
    switch (event.type) {
      case 'node-added':
      case 'node-updated':
      case 'node-deleted':
      case 'node-moved':
        this.render();
        break;
    }
  }

  /**
   * 渲染思维导图
   */
  public render(): void {
    const nodes = this.engine.getAllNodes();
    const links = this.generateLinks(nodes);

    this.renderLinks(links);
    this.renderNodes(nodes);
  }

  /**
   * 生成连接线数据
   */
  private generateLinks(nodes: MindMapNode[]): Array<{source: MindMapNode, target: MindMapNode}> {
    const links: Array<{source: MindMapNode, target: MindMapNode}> = [];
    const nodeMap = new Map(nodes.map(n => [n.id, n]));

    for (const node of nodes) {
      if (node.parentId) {
        const parent = nodeMap.get(node.parentId);
        if (parent) {
          links.push({ source: parent, target: node });
        }
      }
    }

    return links;
  }

  /**
   * 渲染连接线
   */
  private renderLinks(links: Array<{source: MindMapNode, target: MindMapNode}>): void {
    const linkSelection = this.g.selectAll('.mindmap-link')
      .data(links, (d: any) => `${d.source.id}-${d.target.id}`);

    // 移除旧的连接线
    linkSelection.exit().remove();

    // 添加新的连接线
    const linkEnter = linkSelection.enter()
      .append('path')
      .attr('class', 'mindmap-link');

    // 更新连接线
    linkSelection.merge(linkEnter)
      .transition()
      .duration(this.config.enableAnimation ? this.config.animationDuration : 0)
      .attr('d', (d) => this.generateLinkPath(d.source, d.target));
  }

  /**
   * 生成连接线路径
   */
  private generateLinkPath(source: MindMapNode, target: MindMapNode): string {
    const sx = source.position.x + 100; // 假设节点宽度为200
    const sy = source.position.y;
    const tx = target.position.x;
    const ty = target.position.y;

    // 使用贝塞尔曲线
    const mx = (sx + tx) / 2;
    return `M${sx},${sy} C${mx},${sy} ${mx},${ty} ${tx},${ty}`;
  }

  /**
   * 渲染节点
   */
  private renderNodes(nodes: MindMapNode[]): void {
    const nodeSelection = this.g.selectAll('.mindmap-node')
      .data(nodes, (d: any) => d.id);

    // 移除旧节点
    nodeSelection.exit().remove();

    // 添加新节点
    const nodeEnter = nodeSelection.enter()
      .append('g')
      .attr('class', (d) => this.getNodeClass(d))
      .call(this.setupNodeInteractions.bind(this));

    // 添加节点矩形
    nodeEnter.append('rect')
      .attr('class', 'mindmap-node-rect');

    // 添加节点文本
    nodeEnter.append('text')
      .attr('class', 'mindmap-node-text');

    // 更新节点
    const nodeUpdate = nodeSelection.merge(nodeEnter);

    // 更新位置和样式
    nodeUpdate
      .transition()
      .duration(this.config.enableAnimation ? this.config.animationDuration : 0)
      .attr('transform', (d) => `translate(${d.position.x}, ${d.position.y})`);

    // 更新矩形
    nodeUpdate.select('.mindmap-node-rect')
      .attr('width', (d) => this.calculateNodeWidth(d))
      .attr('height', (d) => this.calculateNodeHeight(d))
      .attr('x', (d) => -this.calculateNodeWidth(d) / 2)
      .attr('y', (d) => -this.calculateNodeHeight(d) / 2);

    // 更新文本
    nodeUpdate.select('.mindmap-node-text')
      .text((d) => this.truncateText(d.text, this.calculateNodeWidth(d) - 20))
      .attr('dy', '0.35em');
  }

  /**
   * 获取节点CSS类名
   */
  private getNodeClass(node: MindMapNode): string {
    const classes = ['mindmap-node'];
    
    if (node.format.type === 'heading') {
      classes.push(`mindmap-node-heading-${node.format.level || 1}`);
    }
    
    if (node.metadata?.isAiAnswer) {
      classes.push('mindmap-node-ai-answer');
    }
    
    return classes.join(' ');
  }

  /**
   * 计算节点宽度
   */
  private calculateNodeWidth(node: MindMapNode): number {
    const baseWidth = 120;
    const textLength = node.text.length;
    return Math.max(baseWidth, Math.min(300, textLength * 8 + 40));
  }

  /**
   * 计算节点高度
   */
  private calculateNodeHeight(node: MindMapNode): number {
    const baseHeight = 40;
    if (node.format.type === 'heading') {
      return baseHeight + (6 - (node.format.level || 1)) * 5;
    }
    return baseHeight;
  }

  /**
   * 截断文本
   */
  private truncateText(text: string, maxWidth: number): string {
    const maxChars = Math.floor(maxWidth / 8);
    if (text.length <= maxChars) {
      return text;
    }
    return text.substring(0, maxChars - 3) + '...';
  }

  /**
   * 设置节点交互
   */
  private setupNodeInteractions(selection: d3.Selection<SVGGElement, MindMapNode, SVGGElement, unknown>): void {
    selection
      .on('click', (event, d) => {
        event.stopPropagation();
        this.notifyNodeClick(d.id);
      })
      .on('dblclick', (event, d) => {
        event.stopPropagation();
        this.notifyNodeDoubleClick(d.id);
      })
      .on('contextmenu', (event, d) => {
        event.preventDefault();
        event.stopPropagation();
        this.notifyContextMenu(d.id, event);
      });
  }

  /**
   * 添加节点点击监听器
   */
  public addNodeClickListener(listener: (nodeId: string) => void): void {
    this.nodeClickListeners.push(listener);
  }

  /**
   * 添加节点双击监听器
   */
  public addNodeDoubleClickListener(listener: (nodeId: string) => void): void {
    this.nodeDoubleClickListeners.push(listener);
  }

  /**
   * 添加右键菜单监听器
   */
  public addContextMenuListener(listener: (nodeId: string, event: MouseEvent) => void): void {
    this.contextMenuListeners.push(listener);
  }

  /**
   * 通知节点点击
   */
  private notifyNodeClick(nodeId: string): void {
    this.nodeClickListeners.forEach(listener => listener(nodeId));
  }

  /**
   * 通知节点双击
   */
  private notifyNodeDoubleClick(nodeId: string): void {
    this.nodeDoubleClickListeners.forEach(listener => listener(nodeId));
  }

  /**
   * 通知右键菜单
   */
  private notifyContextMenu(nodeId: string, event: MouseEvent): void {
    this.contextMenuListeners.forEach(listener => listener(nodeId, event));
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<RenderConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.svg
      .attr('width', this.config.width)
      .attr('height', this.config.height);
    this.render();
  }

  /**
   * 居中显示
   */
  public centerView(): void {
    const nodes = this.engine.getAllNodes();
    if (nodes.length === 0) return;

    const bounds = this.calculateBounds(nodes);
    const centerX = (bounds.minX + bounds.maxX) / 2;
    const centerY = (bounds.minY + bounds.maxY) / 2;
    
    const transform = d3.zoomIdentity
      .translate(this.config.width / 2 - centerX, this.config.height / 2 - centerY);
    
    this.svg.transition()
      .duration(this.config.animationDuration)
      .call(this.zoom.transform, transform);
  }

  /**
   * 计算节点边界
   */
  private calculateBounds(nodes: MindMapNode[]): {minX: number, maxX: number, minY: number, maxY: number} {
    let minX = Infinity, maxX = -Infinity, minY = Infinity, maxY = -Infinity;
    
    for (const node of nodes) {
      const width = this.calculateNodeWidth(node);
      const height = this.calculateNodeHeight(node);
      
      minX = Math.min(minX, node.position.x - width / 2);
      maxX = Math.max(maxX, node.position.x + width / 2);
      minY = Math.min(minY, node.position.y - height / 2);
      maxY = Math.max(maxY, node.position.y + height / 2);
    }
    
    return { minX, maxX, minY, maxY };
  }

  /**
   * 销毁渲染器
   */
  public destroy(): void {
    d3.select(this.container).selectAll('*').remove();
    this.nodeClickListeners = [];
    this.nodeDoubleClickListeners = [];
    this.contextMenuListeners = [];
  }
}
