# 双向思维导图插件

一个为 Obsidian 设计的强大插件，提供 Markdown 文档与思维导图的实时双向转换功能，并集成本地 Ollama AI 问答能力。

## 🌟 核心功能

### 📝 双向同步
- **实时转换**：Markdown 文档与思维导图之间的实时双向同步
- **格式保持**：完整保留标题层级、文本样式（粗体、斜体、代码）、列表格式
- **位置映射**：精确的文档位置与思维导图节点映射关系

### 🤖 AI 智能助手
- **本地 AI**：集成本地 Ollama 服务，保护数据隐私
- **多种功能**：AI 问答、文本解释、内容扩展、文档总结
- **上下文理解**：基于选中文本和周围内容提供精准回答
- **思维导图生成**：AI 辅助生成思维导图结构建议

### 🎨 可视化界面
- **D3.js 渲染**：基于 D3.js 的高性能思维导图渲染
- **交互操作**：支持节点拖拽、编辑、删除等操作
- **样式丰富**：不同类型节点的差异化视觉样式
- **响应式设计**：适配不同屏幕尺寸

## 🚀 快速开始

### 前置要求
1. **Obsidian 1.4.0+**
2. **本地 Ollama 服务**（用于 AI 功能）
   ```bash
   # 安装 Ollama
   curl -fsSL https://ollama.ai/install.sh | sh
   
   # 下载模型（例如 llama2）
   ollama pull llama2
   
   # 启动服务
   ollama serve
   ```

### 安装插件
1. 下载插件文件到 `.obsidian/plugins/bidirectional-mindmap/`
2. 在 Obsidian 设置中启用插件
3. 配置 Ollama 服务地址（默认：http://localhost:11434）

## 📖 使用指南

### 基本操作

#### 1. 打开思维导图视图
- 点击左侧功能区的 🧠 图标
- 使用命令面板：`切换思维导图视图`
- 快捷键：`Ctrl+M`（可自定义）

#### 2. Markdown 与思维导图同步
插件会自动解析当前 Markdown 文档并生成对应的思维导图：

```markdown
# 主标题
## 二级标题
这是一段包含**粗体**和*斜体*的文字。

- 无序列表项1
- 无序列表项2

1. 有序列表项1
2. 有序列表项2
```

对应的思维导图结构：
- 主标题（根节点）
  - 二级标题
    - 段落文本（保留样式）
    - 列表项节点

#### 3. AI 功能使用

**选中文本后右键菜单：**
- **AI 问答**：基于选中内容提问
- **AI 解释**：解释选中文本的含义
- **AI 扩展**：扩展选中内容的详细信息

**命令面板功能：**
- `AI 总结文档`：生成整个文档的摘要
- `AI 生成思维导图建议`：为指定主题生成思维导图结构

### 高级功能

#### 1. 节点操作
- **双击节点**：编辑节点文本
- **右键节点**：显示上下文菜单
  - 编辑节点
  - 删除节点
  - AI 解释此节点
- **拖拽节点**：调整节点位置（视觉效果）

#### 2. 思维导图导航
- **鼠标滚轮**：缩放视图
- **拖拽空白区域**：平移视图
- **居中按钮**：自动居中显示所有节点

#### 3. 样式映射规则

| Markdown 格式 | 思维导图样式 |
|---------------|--------------|
| `# 标题` | 大号粗体，彩色背景 |
| `## 标题` | 中号粗体，浅色背景 |
| `**粗体**` | 粗体文本 |
| `*斜体*` | 斜体文本 |
| `` `代码` `` | 等宽字体 |
| `- 列表` | 小号节点，边框样式 |
| AI 回答 | 虚线边框，特殊颜色 |

## ⚙️ 配置选项

### 插件设置
在 Obsidian 设置 → 插件选项 → 双向思维导图 中配置：

- **Ollama 服务器地址**：AI 服务的地址（默认：http://localhost:11434）
- **默认 AI 模型**：使用的 AI 模型（默认：llama2）
- **思维导图视图位置**：右侧/左侧/底部
- **自动同步**：是否启用实时同步
- **同步延迟**：同步延迟时间（100-2000ms）
- **显示 AI 回答标识**：是否在思维导图中标识 AI 回答

### AI 模型推荐
- **llama2**：通用对话模型，适合大多数场景
- **codellama**：代码相关内容的解释和生成
- **mistral**：轻量级模型，响应速度快
- **neural-chat**：对话优化模型

## 🔧 开发指南

### 项目结构
```
src/
├── types.ts              # 类型定义
├── utils.ts              # 工具函数
├── markdown-parser.ts    # Markdown 解析引擎
├── mindmap-engine.ts     # 思维导图节点引擎
├── mindmap-renderer.ts   # D3.js 渲染器
├── sync-controller.ts    # 双向同步控制器
└── ai-service.ts         # AI 服务接口
```

### 本地开发
```bash
# 安装依赖
npm install

# 开发模式（监听文件变化）
npm run dev

# 构建生产版本
npm run build

# 运行测试
npm run test
```

## 🐛 故障排除

### 常见问题

**1. 思维导图不显示**
- 检查是否启用了插件
- 确认当前文档是 Markdown 格式
- 尝试重新打开思维导图视图

**2. AI 功能不工作**
- 确认 Ollama 服务正在运行：`curl http://localhost:11434/api/tags`
- 检查插件设置中的服务器地址
- 确认已下载所需的 AI 模型

**3. 同步延迟或失效**
- 调整设置中的同步延迟时间
- 检查文档是否包含复杂的 Markdown 语法
- 尝试重新加载插件

**4. 性能问题**
- 对于大型文档（>10000 字），考虑分段处理
- 调整思维导图渲染配置
- 关闭不必要的动画效果

### 日志调试
打开开发者工具（F12），查看控制台输出：
```javascript
// 启用详细日志
localStorage.setItem('mindmap-debug', 'true');
```

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

### 开发规范
- 使用 TypeScript 进行开发
- 遵循 ESLint 代码规范
- 添加适当的注释和文档
- 编写测试用例

### 提交格式
```
feat: 添加新功能
fix: 修复问题
docs: 更新文档
style: 代码格式调整
refactor: 代码重构
test: 添加测试
```

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

- [Obsidian](https://obsidian.md/) - 强大的知识管理工具
- [D3.js](https://d3js.org/) - 数据可视化库
- [Ollama](https://ollama.ai/) - 本地 AI 服务
- [markdown-it](https://github.com/markdown-it/markdown-it) - Markdown 解析器

---

如果这个插件对您有帮助，请考虑给项目点个 ⭐️！
