import { App } from 'obsidian';
import { Transformer } from 'markmap-lib';
import { Markmap } from 'markmap-view';
import * as d3 from 'd3';
import { MindMapNode } from '../types';
import { MindMapParser } from './mindmap-parser';
import { DEFAULT_MARKMAP_OPTIONS, CONTAINER_CONFIG, TIMING_CONFIG } from '../utils/constants';

/**
 * 思维导图渲染器
 */
export class MindMapRenderer {
    private transformer: Transformer;
    private mindmap: Markmap | null = null;

    constructor() {
        this.transformer = new Transformer();
    }

    /**
     * 渲染思维导图到指定容器
     */
    async renderToContainer(
        container: HTMLElement, 
        rootNode: MindMapNode,
        onNodeClick?: (nodeId: string) => void,
        onNodeDoubleClick?: (nodeId: string) => void
    ): Promise<boolean> {
        try {
            console.log('开始渲染思维导图到容器');

            // 清空容器
            container.innerHTML = '';

            // 等待容器完全渲染后再获取尺寸
            await new Promise(resolve => setTimeout(resolve, TIMING_CONFIG.RENDER_DELAY));
            
            // 计算容器尺寸
            const { width, height } = this.calculateContainerSize(container);
            console.log('容器尺寸 (after padding):', width, height);

            // 创建SVG元素
            const svg = this.createSVGElement(container, width, height);

            // 等待下一帧再创建markmap，确保SVG已经渲染
            return new Promise((resolve) => {
                requestAnimationFrame(() => {
                    try {
                        // 创建markmap实例
                        this.mindmap = this.createMarkmapInstance(svg, width);

                        // 转换数据并渲染
                        const success = this.renderMindMapData(rootNode);
                        
                        // 设置事件监听器
                        if (success && (onNodeClick || onNodeDoubleClick)) {
                            this.setupEventListeners(container, onNodeClick, onNodeDoubleClick);
                        }

                        resolve(success);
                    } catch (error) {
                        console.error('渲染思维导图时出错:', error);
                        this.createFallbackDisplay(container);
                        resolve(false);
                    }
                });
            });

        } catch (error) {
            console.error('渲染思维导图失败:', error);
            this.createFallbackDisplay(container);
            return false;
        }
    }

    /**
     * 计算容器尺寸
     */
    private calculateContainerSize(container: HTMLElement): { width: number; height: number } {
        const containerRect = container.getBoundingClientRect();
        let width = containerRect.width;
        let height = containerRect.height;

        // 减去内边距（CSS中设置了20px padding）
        width = Math.max(width - CONTAINER_CONFIG.PADDING, CONTAINER_CONFIG.MIN_WIDTH);
        height = Math.max(height - CONTAINER_CONFIG.PADDING, CONTAINER_CONFIG.MIN_HEIGHT);

        // 如果容器尺寸仍然无效，使用默认值
        if (!width || width <= 0 || isNaN(width)) {
            width = CONTAINER_CONFIG.DEFAULT_WIDTH;
        }
        if (!height || height <= 0 || isNaN(height)) {
            height = CONTAINER_CONFIG.DEFAULT_HEIGHT;
        }

        return { width, height };
    }

    /**
     * 创建SVG元素
     */
    private createSVGElement(container: HTMLElement, width: number, height: number): d3.Selection<SVGSVGElement, unknown, null, undefined> {
        return d3.select(container)
            .append('svg')
            .attr('width', '100%')
            .attr('height', '100%')
            .attr('viewBox', `0 0 ${width} ${height}`)
            .attr('preserveAspectRatio', 'xMidYMid meet')
            .style('width', '100%')
            .style('height', '100%')
            .style('min-width', width + 'px')
            .style('min-height', height + 'px');
    }

    /**
     * 创建markmap实例
     */
    private createMarkmapInstance(svg: d3.Selection<SVGSVGElement, unknown, null, undefined>, width: number): Markmap {
        return Markmap.create(svg.node() as SVGSVGElement, {
            ...DEFAULT_MARKMAP_OPTIONS,
            maxWidth: Math.max(width - 100, 200), // 确保最小宽度
        });
    }

    /**
     * 渲染思维导图数据
     */
    private renderMindMapData(rootNode: MindMapNode): boolean {
        try {
            // 转换数据格式
            const markdown = MindMapParser.mindmapNodeToMarkdown(rootNode);
            const { root: data } = this.transformer.transform(markdown);

            console.log('Markmap data:', data);

            // 为每个节点添加自定义ID
            this.addCustomNodeIds(data, rootNode);

            // 设置数据
            this.mindmap!.setData(data);

            return true;
        } catch (error) {
            console.error('渲染思维导图数据时出错:', error);
            return false;
        }
    }

    /**
     * 为markmap数据添加自定义ID
     */
    private addCustomNodeIds(markmapNode: any, mindmapNode: MindMapNode): void {
        markmapNode.mindmapId = mindmapNode.id;
        
        if (markmapNode.children && mindmapNode.children) {
            for (let i = 0; i < Math.min(markmapNode.children.length, mindmapNode.children.length); i++) {
                this.addCustomNodeIds(markmapNode.children[i], mindmapNode.children[i]);
            }
        }
    }

    /**
     * 设置事件监听器
     */
    private setupEventListeners(
        container: HTMLElement,
        onNodeClick?: (nodeId: string) => void,
        onNodeDoubleClick?: (nodeId: string) => void
    ): void {
        if (!onNodeClick && !onNodeDoubleClick) return;

        container.addEventListener('click', (event) => {
            const target = event.target as Element;
            const nodeElement = target.closest('[data-mindmap-id]');
            
            if (nodeElement && onNodeClick) {
                const nodeId = nodeElement.getAttribute('data-mindmap-id');
                if (nodeId) {
                    onNodeClick(nodeId);
                }
            }
        });

        if (onNodeDoubleClick) {
            container.addEventListener('dblclick', (event) => {
                const target = event.target as Element;
                const nodeElement = target.closest('[data-mindmap-id]');
                
                if (nodeElement) {
                    const nodeId = nodeElement.getAttribute('data-mindmap-id');
                    if (nodeId) {
                        onNodeDoubleClick(nodeId);
                    }
                }
            });
        }
    }

    /**
     * 创建备用显示
     */
    private createFallbackDisplay(container: HTMLElement): void {
        container.innerHTML = '';
        const fallbackDiv = container.createDiv('mindmap-fallback');
        fallbackDiv.style.cssText = `
            padding: 20px;
            text-align: center;
            color: var(--text-muted);
            font-size: 14px;
        `;

        fallbackDiv.innerHTML = `
            <h3>思维导图渲染失败</h3>
            <p>请尝试以下操作：</p>
            <ul style="text-align: left; display: inline-block;">
                <li>重新加载插件</li>
                <li>检查浏览器控制台的错误信息</li>
                <li>使用调试命令查看详细信息</li>
            </ul>
            <button onclick="this.closest('.mindmap-container').dispatchEvent(new CustomEvent('retry-render'))">
                重试渲染
            </button>
        `;

        // 添加重试事件监听器
        container.addEventListener('retry-render', () => {
            // 这里需要外部提供重试回调
            console.log('用户请求重试渲染');
        });
    }

    /**
     * 高亮选中的节点
     */
    highlightNode(nodeId: string): void {
        if (!this.mindmap) return;

        // 移除之前的高亮
        d3.selectAll('.selected-node').classed('selected-node', false);

        // 添加新的高亮
        d3.selectAll(`[data-mindmap-id="${nodeId}"]`)
            .classed('selected-node', true);
    }

    /**
     * 适应视图大小
     */
    fit(): void {
        if (this.mindmap) {
            this.mindmap.fit();
        }
    }

    /**
     * 销毁渲染器
     */
    destroy(): void {
        if (this.mindmap) {
            this.mindmap.destroy();
            this.mindmap = null;
        }
    }

    /**
     * 获取当前的markmap实例
     */
    getMarkmap(): Markmap | null {
        return this.mindmap;
    }
}
