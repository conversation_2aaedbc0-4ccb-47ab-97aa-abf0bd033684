{"name": "obsidian-mind-map", "version": "1.1.0", "description": "An Obsidian plugin for displaying markdown notes as mind maps using Markmap", "main": "main.js", "scripts": {"dev": "rollup --config rollup.config.js -w", "build": "rollup --config rollup.config.js", "test": "cross-env TS_NODE_COMPILER_OPTIONS='{ \"module\": \"commonjs\" }' mocha -r ts-node/register -r ignore-styles -r jsdom-global/register tests/**/*.test.ts", "coverage": "nyc -r lcov -e .ts -x \"*.test.ts\" npm run test"}, "keywords": [], "author": "", "license": "MIT", "devDependencies": {"@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "@rollup/plugin-typescript": "^6.0.0", "@types/chai": "^4.2.14", "@types/mocha": "^8.0.4", "@types/node": "^14.14.9", "chai": "^4.2.0", "cross-env": "^7.0.2", "ignore-styles": "^5.0.1", "jsdom": "^16.4.0", "jsdom-global": "^3.0.2", "mocha": "^8.2.0", "nyc": "^15.1.0", "obsidian": "https://github.com/obsidianmd/obsidian-api/tarball/master", "rollup": "^2.33.3", "rollup-plugin-copy": "^3.3.0", "ts-node": "^9.0.0", "tslib": "^2.0.3", "typescript": "^4.1.2"}, "dependencies": {"markmap-lib": "^0.10.2", "markmap-view": "^0.1.2"}}