# Obsidian双向思维导图插件设计文档（修订版）

## 一、项目概述

### 1.1 目标与背景
本插件为Obsidian提供Markdown文档与思维导图的实时双向转换功能，结合本地Ollama AI问答能力，帮助用户通过可视化与结构化结合的方式高效管理知识。核心解决现有插件中Markdown与思维导图同步深度不足、格式适配性差的问题。

### 1.2 核心功能
- Markdown与思维导图基于标题结构和内容的双向实时同步
- 选中文字通过右键菜单触发本地Ollama AI提问，回答同步至文档与思维导图
- 支持Markdown格式（标题层级、文本样式）与思维导图节点属性的精准映射

## 二、系统架构设计

### 2.1 整体架构
```
+-------------------+       +-------------------+
|  Markdown编辑器   |<----->|   思维导图视图    |
+-------------------+       +-------------------+
        |                           |
        v                           v
+-------------------+       +-------------------+
|  格式解析引擎     |<----->|  节点结构引擎     |
+-------------------+       +-------------------+
        |                           |
        v                           v
+-------------------+       +-------------------+
|  实时同步控制器   |<----->|   AI交互模块      |
+-------------------+       +-------------------+
                                        |
                                        v
                              +-------------------+
                              |  本地Ollama接口   |
                              +-------------------+
```

### 2.2 模块职责

#### 2.2.1 格式解析引擎
- **核心职责**：将Markdown文本解析为“格式+内容”的结构化数据，支持逆向转换
- 解析范围：标题层级（#至######）、文本样式（粗体、斜体、代码块）、段落分隔、列表（有序/无序）
- 输出数据：包含“格式标识”“内容文本”“父节点ID”“位置索引”的JSON结构

#### 2.2.2 节点结构引擎
- **核心职责**：管理思维导图的节点层级与属性，支持与Markdown格式的映射
- 节点属性：包含“文本内容”“格式标识”（对应Markdown样式）、“层级深度”“子节点列表”“关联文本位置”
- 提供节点增删、层级调整、属性修改的API接口

#### 2.2.3 实时同步控制器
- **核心职责**：监听两边修改并触发同步，维护格式与内容的一致性
- 监听对象：Markdown编辑器的文本变更事件（内容/格式修改）、思维导图的节点操作事件（增删/移动/编辑）
- 同步逻辑：通过“位置索引-节点ID”映射表，定位修改区域并双向更新

#### 2.2.4 AI交互模块
- **核心职责**：处理用户提问请求，对接本地Ollama接口，同步回答至双视图
- 功能：接收选中文本→构造提问参数→调用Ollama接口→解析回答→生成Markdown片段与思维导图节点

## 三、核心功能设计（增强版）

### 3.1 Markdown与思维导图双向转换规则

#### 3.1.1 Markdown到思维导图的解析规则（含格式映射）
1. **标题层级解析**
   - 一级标题（# 标题）→ 思维导图根节点，格式标识为“h1”
   - 二级标题（## 标题）→ 根节点直属子节点，格式标识为“h2”
   - 以此类推，最多支持6级标题（h6），超过层级自动归为h6子节点
   - 标题内文本样式（如**粗体**、*斜体*）→ 节点文本保留样式标记，格式标识追加“bold”“italic”

2. **正文内容解析**
   - 段落文本 → 父标题节点的“内容子节点”，格式标识为“paragraph”
   - 段落内文本样式（如`代码`、[链接](url)）→ 节点文本保留原格式，格式标识记录“code”“link”
   - 有序列表（1. 项）→ 父节点的子节点组，格式标识为“ordered-list”，节点按序号排序
   - 无序列表（- 项）→ 父节点的子节点组，格式标识为“unordered-list”

3. **示例映射**
   ```markdown
   # 根标题
   ## 二级标题
   这是一段包含**粗体**和*斜体*的段落。
   - 无序列表项1
   - 无序列表项2
   ```
   解析为思维导图结构：
   ```json
   {
     "id": "root",
     "text": "根标题",
     "format": "h1",
     "children": [
       {
         "id": "n1",
         "text": "二级标题",
         "format": "h2",
         "children": [
           {
             "id": "n2",
             "text": "这是一段包含**粗体**和*斜体*的段落。",
             "format": "paragraph bold italic",
             "children": []
           },
           {
             "id": "n3",
             "text": "无序列表项1",
             "format": "unordered-list",
             "children": []
           },
           {
             "id": "n4",
             "text": "无序列表项2",
             "format": "unordered-list",
             "children": []
           }
         ]
       }
     ]
   }
   ```

#### 3.1.2 思维导图到Markdown的转换规则
1. **节点到文本的转换**
   - 根节点（h1）→ # 节点文本
   - 子节点（h2-h6）→ 对应数量# + 节点文本（如h3→### 文本）
   - 内容子节点（paragraph）→ 直接转换为段落文本，保留样式标记
   - 列表节点（ordered-list/unordered-list）→ 转换为对应列表格式（1. / -）

2. **格式标识的逆向转换**
   - 节点格式标识“bold”→ 文本包裹**
   - 标识“italic”→ 文本包裹*
   - 标识“code”→ 文本包裹`
   - 标识“link”→ 转换为[文本](url)格式（需从节点元数据提取url）

### 3.2 实时同步机制
1. **Markdown修改触发同步**
   - 监听事件：文本插入/删除、格式变更（如标题层级调整、加粗）
   - 处理逻辑：
     - 通过格式解析引擎提取修改区域的“格式+内容”
     - 定位映射的思维导图节点ID（通过位置索引映射表）
     - 调用节点结构引擎更新对应节点的文本或属性

2. **思维导图修改触发同步**
   - 监听事件：节点文本修改、节点移动（层级变更）、节点删除/添加
   - 处理逻辑：
     - 提取节点的新属性（文本、格式、层级）
     - 定位对应Markdown中的位置索引
     - 生成对应的Markdown文本片段，替换原位置内容

3. **冲突处理**
   - 当两边同时修改同一内容时，以最后一次修改时间为准
   - 冲突时在控制台记录日志，保留修改前快照（支持手动回溯）

### 3.3 AI问答功能设计
1. **用户操作流程**
   - 选中Markdown中的文本（支持跨段落/标题选择）
   - 右键菜单选择“AI提问”→ 弹出输入框（默认问题：“解释选中内容”，可自定义）
   - 提交后，插件调用本地Ollama接口，显示加载状态
   - 接收回答后，自动在选中内容后插入回答（Markdown格式），并同步生成思维导图节点

2. **Ollama接口交互**
   - 请求参数：
     ```json
     {
       "model": "用户配置的模型（默认llama2）",
       "prompt": "问题：[用户输入]；上下文：[选中的文本内容]",
       "stream": false  // 非流式返回，便于同步处理
     }
     ```
   - 响应处理：
     - 解析返回的回答文本，转换为Markdown格式（自动补全段落、列表等）
     - 生成对应的思维导图节点（父节点为选中内容所在的最近标题节点）
     - 节点格式标识设为“ai-answer”（可自定义样式）

3. **同步逻辑**
   - 回答插入位置：选中内容的末尾（若选中段落则在段落后，选中标题则在标题下第一个内容前）
   - 思维导图节点位置：作为选中内容对应节点的同级子节点，前置标识“AI：”

## 四、数据结构设计

### 4.1 Markdown解析数据结构
```typescript
interface ParsedMarkdownNode {
  type: 'heading' | 'paragraph' | 'ordered-list' | 'unordered-list'; // 节点类型
  level: number; // 仅heading有效（1-6）
  content: string; // 文本内容（含样式标记，如**bold**）
  styles: string[]; // 文本样式（如['bold', 'italic']）
  startIndex: number; // 在原文档中的起始位置索引
  endIndex: number; // 在原文档中的结束位置索引
  parentId: string; // 父节点ID（映射思维导图节点）
  children?: ParsedMarkdownNode[]; // 子节点（如列表项、子标题）
}
```

### 4.2 思维导图节点数据结构
```typescript
interface MindMapNode {
  id: string; // 唯一标识（UUID生成）
  text: string; // 节点文本（含样式标记）
  format: {
    type: 'heading' | 'paragraph' | 'ordered-list' | 'unordered-list';
    level?: number; // 标题层级
    styles: string[]; // 文本样式
  };
  parentId: string; // 父节点ID（根节点parentId为null）
  position: { // 用于渲染定位（不影响同步）
    x: number;
    y: number;
  };
  metadata?: { // 扩展信息（如链接url、AI回答标识）
    isAiAnswer?: boolean;
    url?: string;
  };
}
```

### 4.3 映射关系表
用于维护Markdown与思维导图的对应关系：
```typescript
interface SyncMap {
  markdownToMindMap: { [startIndex: number]: string }; // Markdown起始索引→节点ID
  mindMapToMarkdown: { [nodeId: string]: { start: number; end: number } }; // 节点ID→Markdown位置范围
}
```

## 五、用户界面设计

### 5.1 思维导图视图
- **布局**：支持与Markdown编辑器分栏显示（左/右/下），可通过快捷键（如Ctrl+M）切换显示/隐藏
- **节点样式**：
  - 标题节点：根据层级显示不同字体大小（h1最大，h6最小）
  - 文本样式：粗体节点文字加粗，斜体节点文字倾斜
  - AI回答节点：左侧添加蓝色标识条，区分原生内容
- **交互**：
  - 双击节点：进入编辑模式（修改文本）
  - 拖拽节点：调整位置（仅视觉效果）或移动层级（触发同步）
  - 右键节点：菜单包含“转换为标题”“添加子节点”“AI提问”等选项

### 5.2 右键菜单与交互元素
- Markdown编辑器右键菜单：
  - “转换为思维导图节点”：将选中文本转为独立节点（适用于非标题/列表文本）
  - “AI提问”：触发AI问答功能
- 思维导图节点右键菜单：
  - “转换为Markdown段落”：将节点转为正文段落（解除标题/列表关联）
  - “复制为Markdown”：将节点文本按对应格式复制到剪贴板

## 六、系统实现方案

### 6.1 核心技术选型
- 开发语言：TypeScript
- Markdown解析：基于Obsidian的`markdown-it`解析器扩展，增加格式与位置索引提取
- 思维导图渲染：基于`d3.js`实现（轻量且支持动态更新节点）
- Ollama接口：使用`node-fetch`发送HTTP请求，处理JSON响应
- 实时监听：利用Obsidian的`Editor`API监听文本变化，自定义事件监听思维导图操作

### 6.2 关键流程实现
1. **文档加载流程**：
   - 打开Markdown文件时，插件自动触发解析
   - 格式解析引擎生成ParsedMarkdownNode树
   - 节点结构引擎基于树生成思维导图节点
   - 建立初始映射关系表，渲染思维导图视图

2. **AI回答同步流程**：
   - 接收Ollama回答→转换为Markdown文本（含格式）
   - 插入Markdown编辑器对应位置→触发同步
   - 格式解析引擎识别新内容→生成思维导图节点（关联父节点）
   - 节点结构引擎添加节点→思维导图视图刷新

## 七、测试与部署

### 7.1 测试重点
- 格式映射准确性：验证10种以上Markdown样式（标题、粗体、列表等）的双向转换一致性
- 同步延迟：测试1000字文档中连续编辑的同步响应时间（目标<100ms）
- 边界场景：超长标题（500字）、深层级节点（10级以上）、大量AI回答插入的稳定性

### 7.2 部署要求
- 依赖环境：Obsidian v1.4.0+，本地Ollama服务运行（默认端口11434）
- 安装方式：通过Obsidian社区插件市场安装，或手动放置插件文件夹至`.obsidian/plugins/`

## 八、未来扩展（精简版）
- 支持更多Markdown元素（表格、图片）与思维导图的映射
- 增加思维导图节点样式自定义（颜色、形状）
- 优化大文档（10万字以上）的同步性能
- 支持AI回答的多轮对话（保留上下文）