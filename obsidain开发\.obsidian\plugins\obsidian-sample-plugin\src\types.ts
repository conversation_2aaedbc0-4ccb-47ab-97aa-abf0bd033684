/**
 * 双向思维导图插件核心数据结构定义
 * 基于设计文档中的数据结构规范
 */

/**
 * Markdown解析节点数据结构
 * 用于表示解析后的Markdown文档结构
 */
export interface ParsedMarkdownNode {
  /** 节点类型 */
  type: 'heading' | 'paragraph' | 'ordered-list' | 'unordered-list';
  /** 标题层级，仅heading类型有效（1-6） */
  level?: number;
  /** 文本内容（含样式标记，如**bold**） */
  content: string;
  /** 文本样式数组（如['bold', 'italic']） */
  styles: string[];
  /** 在原文档中的起始位置索引 */
  startIndex: number;
  /** 在原文档中的结束位置索引 */
  endIndex: number;
  /** 父节点ID（映射思维导图节点） */
  parentId: string | null;
  /** 子节点数组（如列表项、子标题） */
  children?: ParsedMarkdownNode[];
  /** 节点唯一标识 */
  id: string;
}

/**
 * 思维导图节点格式信息
 */
export interface NodeFormat {
  /** 节点类型 */
  type: 'heading' | 'paragraph' | 'ordered-list' | 'unordered-list';
  /** 标题层级 */
  level?: number;
  /** 文本样式数组 */
  styles: string[];
}

/**
 * 思维导图节点位置信息
 */
export interface NodePosition {
  /** X坐标 */
  x: number;
  /** Y坐标 */
  y: number;
}

/**
 * 思维导图节点元数据
 */
export interface NodeMetadata {
  /** 是否为AI回答节点 */
  isAiAnswer?: boolean;
  /** 链接URL */
  url?: string;
  /** 创建时间戳 */
  createdAt?: number;
  /** 最后修改时间戳 */
  lastModified?: number;
}

/**
 * 思维导图节点数据结构
 * 用于表示思维导图中的节点信息
 */
export interface MindMapNode {
  /** 唯一标识（UUID生成） */
  id: string;
  /** 节点文本（含样式标记） */
  text: string;
  /** 格式信息 */
  format: NodeFormat;
  /** 父节点ID（根节点parentId为null） */
  parentId: string | null;
  /** 子节点ID数组 */
  children: string[];
  /** 用于渲染定位（不影响同步） */
  position: NodePosition;
  /** 扩展信息 */
  metadata?: NodeMetadata;
}

/**
 * 映射关系表
 * 用于维护Markdown与思维导图的对应关系
 */
export interface SyncMap {
  /** Markdown起始索引→节点ID的映射 */
  markdownToMindMap: { [startIndex: number]: string };
  /** 节点ID→Markdown位置范围的映射 */
  mindMapToMarkdown: { [nodeId: string]: { start: number; end: number } };
}

/**
 * 插件设置接口
 */
export interface MindMapPluginSettings {
  /** Ollama服务器地址 */
  ollamaUrl: string;
  /** 默认使用的AI模型 */
  defaultModel: string;
  /** 思维导图视图位置 */
  viewPosition: 'right' | 'left' | 'bottom';
  /** 是否自动同步 */
  autoSync: boolean;
  /** 同步延迟（毫秒） */
  syncDelay: number;
  /** 是否显示AI回答标识 */
  showAiIndicator: boolean;
}

/**
 * AI问答请求参数
 */
export interface OllamaRequest {
  /** 使用的模型名称 */
  model: string;
  /** 提问内容 */
  prompt: string;
  /** 是否流式返回 */
  stream: boolean;
  /** 上下文信息 */
  context?: string;
}

/**
 * AI问答响应数据
 */
export interface OllamaResponse {
  /** 回答内容 */
  response: string;
  /** 是否完成 */
  done: boolean;
  /** 错误信息 */
  error?: string;
}

/**
 * 同步事件类型
 */
export type SyncEventType = 
  | 'markdown-changed'
  | 'mindmap-changed'
  | 'node-added'
  | 'node-removed'
  | 'node-moved'
  | 'node-edited';

/**
 * 同步事件数据
 */
export interface SyncEvent {
  /** 事件类型 */
  type: SyncEventType;
  /** 相关节点ID */
  nodeId?: string;
  /** 变更的文本范围 */
  range?: { start: number; end: number };
  /** 新的内容 */
  newContent?: string;
  /** 旧的内容 */
  oldContent?: string;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 文档解析结果
 */
export interface ParseResult {
  /** 解析得到的节点树 */
  nodes: ParsedMarkdownNode[];
  /** 映射关系 */
  syncMap: SyncMap;
  /** 解析错误信息 */
  errors?: string[];
}

/**
 * 思维导图渲染配置
 */
export interface RenderConfig {
  /** 容器宽度 */
  width: number;
  /** 容器高度 */
  height: number;
  /** 节点间距 */
  nodeSpacing: number;
  /** 层级间距 */
  levelSpacing: number;
  /** 是否启用动画 */
  enableAnimation: boolean;
  /** 动画持续时间 */
  animationDuration: number;
}

/**
 * 右键菜单项配置
 */
export interface ContextMenuItem {
  /** 菜单项标题 */
  title: string;
  /** 菜单项图标 */
  icon?: string;
  /** 点击回调函数 */
  callback: () => void;
  /** 是否可用 */
  enabled?: boolean;
  /** 分隔符 */
  separator?: boolean;
}

/**
 * 插件状态
 */
export interface PluginState {
  /** 当前活动的文档 */
  activeDocument?: string;
  /** 思维导图是否可见 */
  mindMapVisible: boolean;
  /** 是否正在同步 */
  syncing: boolean;
  /** 最后同步时间 */
  lastSyncTime?: number;
}
