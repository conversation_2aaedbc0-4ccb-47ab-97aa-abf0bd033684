/**
 * 双向同步控制器
 * 负责Markdown编辑器与思维导图之间的实时双向同步
 */

import { Editor, MarkdownView } from 'obsidian';
import { MarkdownParser } from './markdown-parser';
import { MindMapEngine, MindMapChangeEvent } from './mindmap-engine';
import { SyncMap, SyncEvent, ParsedMarkdownNode, MindMapNode } from './types';
import { debounce, findTextPosition, getMarkdownPrefix } from './utils';

export class SyncController {
  private editor: Editor | null = null;
  private view: MarkdownView | null = null;
  private parser: MarkdownParser;
  private engine: MindMapEngine;
  private syncMap: SyncMap = { markdownToMindMap: {}, mindMapToMarkdown: {} };
  private isUpdating = false;
  private syncDelay = 300;
  private changeListeners: ((event: SyncEvent) => void)[] = [];

  // 防抖函数
  private debouncedMarkdownSync = debounce(this.syncFromMarkdown.bind(this), this.syncDelay);
  private debouncedMindMapSync = debounce(this.syncFromMindMap.bind(this), this.syncDelay);

  constructor(parser: MarkdownParser, engine: MindMapEngine) {
    this.parser = parser;
    this.engine = engine;
    this.bindEngineEvents();
  }

  /**
   * 设置编辑器和视图
   * @param editor Obsidian编辑器实例
   * @param view Markdown视图实例
   */
  public setEditor(editor: Editor, view: MarkdownView): void {
    this.unbindEditorEvents();
    this.editor = editor;
    this.view = view;
    this.bindEditorEvents();
    this.initialSync();
  }

  /**
   * 初始同步
   */
  private initialSync(): void {
    if (!this.editor) return;

    const content = this.editor.getValue();
    const parseResult = this.parser.parseMarkdown(content);
    
    if (parseResult.errors && parseResult.errors.length > 0) {
      console.warn('Markdown parsing errors:', parseResult.errors);
    }

    this.syncMap = parseResult.syncMap;
    this.engine.createFromMarkdownNodes(parseResult.nodes);

    this.notifyChange({
      type: 'markdown-changed',
      timestamp: Date.now(),
      newContent: content
    });
  }

  /**
   * 绑定编辑器事件
   */
  private bindEditorEvents(): void {
    if (!this.editor) return;

    // 监听文档变更
    this.editor.on('change', this.handleMarkdownChange.bind(this));
  }

  /**
   * 解绑编辑器事件
   */
  private unbindEditorEvents(): void {
    if (this.editor) {
      this.editor.off('change', this.handleMarkdownChange.bind(this));
    }
  }

  /**
   * 绑定引擎事件
   */
  private bindEngineEvents(): void {
    this.engine.addChangeListener(this.handleMindMapChange.bind(this));
  }

  /**
   * 处理Markdown变更
   */
  private handleMarkdownChange(editor: Editor, changeObj: any): void {
    if (this.isUpdating) return;

    this.debouncedMarkdownSync();
  }

  /**
   * 处理思维导图变更
   */
  private handleMindMapChange(event: MindMapChangeEvent): void {
    if (this.isUpdating) return;

    this.debouncedMindMapSync(event);
  }

  /**
   * 从Markdown同步到思维导图
   */
  private syncFromMarkdown(): void {
    if (!this.editor || this.isUpdating) return;

    this.isUpdating = true;

    try {
      const content = this.editor.getValue();
      const parseResult = this.parser.parseMarkdown(content);

      if (parseResult.errors && parseResult.errors.length > 0) {
        console.warn('Markdown parsing errors:', parseResult.errors);
      }

      // 更新同步映射
      this.syncMap = parseResult.syncMap;

      // 重新创建思维导图节点
      this.engine.createFromMarkdownNodes(parseResult.nodes);

      this.notifyChange({
        type: 'markdown-changed',
        timestamp: Date.now(),
        newContent: content
      });

    } catch (error) {
      console.error('Error syncing from Markdown:', error);
    } finally {
      this.isUpdating = false;
    }
  }

  /**
   * 从思维导图同步到Markdown
   */
  private syncFromMindMap(event: MindMapChangeEvent): void {
    if (!this.editor || this.isUpdating) return;

    this.isUpdating = true;

    try {
      const nodes = this.engine.getAllNodes();
      const markdownNodes = this.convertMindMapToMarkdownNodes(nodes);
      const newContent = this.parser.nodesToMarkdown(markdownNodes);

      // 更新编辑器内容
      const currentContent = this.editor.getValue();
      if (currentContent !== newContent) {
        this.editor.setValue(newContent);
        
        // 更新同步映射
        this.syncMap = this.parser.updatePositionIndices(markdownNodes, newContent);
      }

      this.notifyChange({
        type: 'mindmap-changed',
        nodeId: event.nodeId,
        timestamp: Date.now(),
        newContent
      });

    } catch (error) {
      console.error('Error syncing from MindMap:', error);
    } finally {
      this.isUpdating = false;
    }
  }

  /**
   * 将思维导图节点转换为Markdown节点
   */
  private convertMindMapToMarkdownNodes(mindMapNodes: MindMapNode[]): ParsedMarkdownNode[] {
    const markdownNodes: ParsedMarkdownNode[] = [];
    const nodeMap = new Map(mindMapNodes.map(n => [n.id, n]));

    // 获取根节点并按位置排序
    const rootNodes = mindMapNodes
      .filter(n => n.parentId === null)
      .sort((a, b) => a.position.y - b.position.y);

    for (const rootNode of rootNodes) {
      const mdNode = this.convertSingleMindMapNode(rootNode, nodeMap);
      markdownNodes.push(mdNode);
    }

    return markdownNodes;
  }

  /**
   * 转换单个思维导图节点
   */
  private convertSingleMindMapNode(
    mindMapNode: MindMapNode, 
    nodeMap: Map<string, MindMapNode>
  ): ParsedMarkdownNode {
    const children: ParsedMarkdownNode[] = [];

    // 递归转换子节点
    for (const childId of mindMapNode.children) {
      const childNode = nodeMap.get(childId);
      if (childNode) {
        children.push(this.convertSingleMindMapNode(childNode, nodeMap));
      }
    }

    return {
      id: mindMapNode.id,
      type: mindMapNode.format.type,
      level: mindMapNode.format.level,
      content: mindMapNode.text,
      styles: mindMapNode.format.styles,
      startIndex: 0, // 将在updatePositionIndices中更新
      endIndex: 0,   // 将在updatePositionIndices中更新
      parentId: mindMapNode.parentId,
      children
    };
  }

  /**
   * 插入AI回答到指定位置
   * @param selectedText 选中的文本
   * @param aiResponse AI回答
   * @param insertPosition 插入位置
   */
  public insertAiResponse(selectedText: string, aiResponse: string, insertPosition?: number): void {
    if (!this.editor) return;

    this.isUpdating = true;

    try {
      const content = this.editor.getValue();
      let insertPos = insertPosition;

      // 如果没有指定位置，查找选中文本的位置
      if (insertPos === undefined) {
        const textPos = findTextPosition(content, selectedText);
        if (textPos) {
          insertPos = textPos.end;
        } else {
          insertPos = content.length;
        }
      }

      // 格式化AI回答
      const formattedResponse = `\n\n**AI回答：**\n${aiResponse}\n`;
      
      // 插入内容
      const newContent = content.slice(0, insertPos) + formattedResponse + content.slice(insertPos);
      this.editor.setValue(newContent);

      // 触发同步
      setTimeout(() => {
        this.isUpdating = false;
        this.syncFromMarkdown();
      }, 100);

    } catch (error) {
      console.error('Error inserting AI response:', error);
      this.isUpdating = false;
    }
  }

  /**
   * 获取选中文本的上下文信息
   * @returns 上下文信息
   */
  public getSelectionContext(): { selectedText: string; context: string; position: number } | null {
    if (!this.editor) return null;

    const selection = this.editor.getSelection();
    if (!selection) return null;

    const content = this.editor.getValue();
    const cursor = this.editor.getCursor();
    const position = this.editor.posToOffset(cursor);

    // 获取上下文（前后各100个字符）
    const contextStart = Math.max(0, position - 100);
    const contextEnd = Math.min(content.length, position + 100);
    const context = content.slice(contextStart, contextEnd);

    return {
      selectedText: selection,
      context,
      position
    };
  }

  /**
   * 更新节点文本
   * @param nodeId 节点ID
   * @param newText 新文本
   */
  public updateNodeText(nodeId: string, newText: string): void {
    const node = this.engine.getNode(nodeId);
    if (!node) return;

    this.engine.updateNode(nodeId, { text: newText });
  }

  /**
   * 移动节点
   * @param nodeId 节点ID
   * @param newParentId 新父节点ID
   * @param insertIndex 插入位置
   */
  public moveNode(nodeId: string, newParentId: string | null, insertIndex?: number): void {
    this.engine.moveNode(nodeId, newParentId, insertIndex);
  }

  /**
   * 删除节点
   * @param nodeId 节点ID
   */
  public deleteNode(nodeId: string): void {
    this.engine.deleteNode(nodeId);
  }

  /**
   * 添加新节点
   * @param parentId 父节点ID
   * @param text 节点文本
   * @param type 节点类型
   */
  public addNode(parentId: string | null, text: string, type: 'heading' | 'paragraph' | 'ordered-list' | 'unordered-list' = 'paragraph'): string {
    const newNode: MindMapNode = {
      id: crypto.randomUUID(),
      text,
      format: { type, styles: [] },
      parentId,
      children: [],
      position: { x: 0, y: 0 },
      metadata: {
        createdAt: Date.now(),
        lastModified: Date.now()
      }
    };

    this.engine.addNode(newNode);
    return newNode.id;
  }

  /**
   * 获取同步映射
   */
  public getSyncMap(): SyncMap {
    return { ...this.syncMap };
  }

  /**
   * 设置同步延迟
   * @param delay 延迟时间（毫秒）
   */
  public setSyncDelay(delay: number): void {
    this.syncDelay = delay;
    this.debouncedMarkdownSync = debounce(this.syncFromMarkdown.bind(this), this.syncDelay);
    this.debouncedMindMapSync = debounce(this.syncFromMindMap.bind(this), this.syncDelay);
  }

  /**
   * 添加变更监听器
   */
  public addChangeListener(listener: (event: SyncEvent) => void): void {
    this.changeListeners.push(listener);
  }

  /**
   * 移除变更监听器
   */
  public removeChangeListener(listener: (event: SyncEvent) => void): void {
    const index = this.changeListeners.indexOf(listener);
    if (index > -1) {
      this.changeListeners.splice(index, 1);
    }
  }

  /**
   * 通知变更
   */
  private notifyChange(event: SyncEvent): void {
    this.changeListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in sync change listener:', error);
      }
    });
  }

  /**
   * 销毁同步控制器
   */
  public destroy(): void {
    this.unbindEditorEvents();
    this.changeListeners = [];
    this.editor = null;
    this.view = null;
  }
}
