/* 双向思维导图插件样式 */

/* 思维导图视图容器 */
.mindmap-view-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: var(--background-primary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
}

/* 功能区图标 */
.mindmap-ribbon-icon {
  color: var(--icon-color);
}

.mindmap-ribbon-icon:hover {
  color: var(--icon-color-hover);
}

/* 状态栏 */
.mindmap-status-bar {
  font-size: 12px;
  color: var(--text-muted);
  cursor: pointer;
}

.mindmap-status-bar:hover {
  color: var(--text-normal);
}

/* 思维导图节点样式 */
.mindmap-node {
  cursor: pointer;
  transition: all 0.2s ease;
}

.mindmap-node:hover {
  filter: brightness(1.1);
}

.mindmap-node-rect {
  fill: var(--background-secondary);
  stroke: var(--border-color);
  stroke-width: 1;
  rx: 5;
  ry: 5;
  transition: all 0.2s ease;
}

.mindmap-node-text {
  fill: var(--text-normal);
  font-family: var(--font-interface);
  font-size: 14px;
  text-anchor: middle;
  dominant-baseline: central;
  pointer-events: none;
  user-select: none;
}

/* 标题节点样式 */
.mindmap-node-heading-1 .mindmap-node-rect {
  fill: var(--color-accent-1);
  stroke: var(--color-accent);
  stroke-width: 2;
}

.mindmap-node-heading-1 .mindmap-node-text {
  font-size: 20px;
  font-weight: bold;
  fill: var(--text-on-accent);
}

.mindmap-node-heading-2 .mindmap-node-rect {
  fill: var(--color-accent-2);
  stroke: var(--color-accent);
}

.mindmap-node-heading-2 .mindmap-node-text {
  font-size: 18px;
  font-weight: bold;
}

.mindmap-node-heading-3 .mindmap-node-rect {
  fill: var(--background-secondary-alt);
  stroke: var(--border-color);
}

.mindmap-node-heading-3 .mindmap-node-text {
  font-size: 16px;
  font-weight: bold;
}

.mindmap-node-heading-4 .mindmap-node-text,
.mindmap-node-heading-5 .mindmap-node-text,
.mindmap-node-heading-6 .mindmap-node-text {
  font-weight: 600;
}

/* AI回答节点样式 */
.mindmap-node-ai-answer .mindmap-node-rect {
  stroke: var(--color-accent);
  stroke-width: 2;
  stroke-dasharray: 5,5;
  fill: var(--background-modifier-success);
}

.mindmap-node-ai-answer .mindmap-node-text {
  fill: var(--text-success);
}

/* 连接线样式 */
.mindmap-link {
  fill: none;
  stroke: var(--border-color);
  stroke-width: 1.5;
  transition: all 0.2s ease;
}

.mindmap-link:hover {
  stroke: var(--color-accent);
  stroke-width: 2;
}

/* 选中状态 */
.mindmap-node-selected .mindmap-node-rect {
  stroke: var(--color-accent);
  stroke-width: 3;
  filter: brightness(1.2);
}

/* 列表节点样式 */
.mindmap-node-ordered-list .mindmap-node-rect,
.mindmap-node-unordered-list .mindmap-node-rect {
  fill: var(--background-modifier-border);
  stroke: var(--border-color);
  rx: 3;
  ry: 3;
}

.mindmap-node-ordered-list .mindmap-node-text,
.mindmap-node-unordered-list .mindmap-node-text {
  font-size: 13px;
}

/* 段落节点样式 */
.mindmap-node-paragraph .mindmap-node-rect {
  fill: var(--background-primary-alt);
  stroke: var(--border-color);
  stroke-width: 1;
  rx: 3;
  ry: 3;
}

.mindmap-node-paragraph .mindmap-node-text {
  font-size: 13px;
  fill: var(--text-muted);
}

/* 模态框样式 */
.modal .modal-content {
  max-width: 500px;
}

.modal textarea,
.modal input[type="text"] {
  width: 100%;
  margin: 10px 0;
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
  color: var(--text-normal);
  font-family: var(--font-interface);
  resize: vertical;
}

.modal textarea:focus,
.modal input[type="text"]:focus {
  outline: none;
  border-color: var(--color-accent);
  box-shadow: 0 0 0 2px var(--color-accent-2);
}

/* 按钮样式 */
.modal button {
  padding: 8px 16px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-secondary);
  color: var(--text-normal);
  cursor: pointer;
  font-family: var(--font-interface);
  transition: all 0.2s ease;
}

.modal button:hover {
  background-color: var(--background-modifier-hover);
}

.modal button.mod-cta {
  background-color: var(--color-accent);
  color: var(--text-on-accent);
  border-color: var(--color-accent);
}

.modal button.mod-cta:hover {
  background-color: var(--color-accent-hover);
}

.modal button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 设置面板样式 */
.setting-item-description {
  color: var(--text-muted);
  font-size: 0.9em;
  margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mindmap-view-container {
    border: none;
    border-radius: 0;
  }

  .mindmap-node-text {
    font-size: 12px;
  }

  .mindmap-node-heading-1 .mindmap-node-text {
    font-size: 16px;
  }

  .mindmap-node-heading-2 .mindmap-node-text {
    font-size: 14px;
  }
}

/* 深色主题适配 */
.theme-dark .mindmap-node-rect {
  fill: var(--background-secondary);
}

.theme-dark .mindmap-node-ai-answer .mindmap-node-rect {
  fill: var(--background-modifier-success);
}

/* 动画效果 */
@keyframes nodeAppear {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.mindmap-node {
  animation: nodeAppear 0.3s ease-out;
}

/* 加载状态 */
.mindmap-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-muted);
  font-size: 14px;
}

.mindmap-loading::before {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--color-accent);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
