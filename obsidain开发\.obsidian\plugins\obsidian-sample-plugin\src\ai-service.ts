/**
 * AI交互模块
 * 负责与本地Ollama API的交互，处理AI问答功能
 */

import { Notice } from 'obsidian';
import { OllamaRequest, OllamaResponse } from './types';
import { validateOllamaConnection } from './utils';

export class AIService {
  private baseUrl: string;
  private defaultModel: string;
  private timeout: number;

  constructor(baseUrl: string = 'http://localhost:11434', defaultModel: string = 'llama2', timeout: number = 30000) {
    this.baseUrl = baseUrl.replace(/\/$/, ''); // 移除末尾斜杠
    this.defaultModel = defaultModel;
    this.timeout = timeout;
  }

  /**
   * 检查Ollama服务连接状态
   */
  public async checkConnection(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tags`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(5000)
      });
      return response.ok;
    } catch (error) {
      console.error('Ollama connection check failed:', error);
      return false;
    }
  }

  /**
   * 获取可用的模型列表
   */
  public async getAvailableModels(): Promise<string[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tags`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(10000)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data.models?.map((model: any) => model.name) || [];
    } catch (error) {
      console.error('Failed to get available models:', error);
      return [];
    }
  }

  /**
   * 发送AI问答请求
   * @param question 问题
   * @param context 上下文信息
   * @param model 使用的模型（可选）
   */
  public async askQuestion(question: string, context?: string, model?: string): Promise<string> {
    const selectedModel = model || this.defaultModel;
    
    // 构建提示词
    let prompt = question;
    if (context) {
      prompt = `基于以下上下文回答问题：\n\n上下文：\n${context}\n\n问题：${question}\n\n请用中文回答：`;
    }

    const request: OllamaRequest = {
      model: selectedModel,
      prompt: prompt,
      stream: false
    };

    try {
      const response = await this.sendRequest('/api/generate', request);
      return response.response || '抱歉，AI没有返回有效回答。';
    } catch (error) {
      console.error('AI question failed:', error);
      throw new Error(`AI问答失败: ${error.message}`);
    }
  }

  /**
   * 解释选中的文本
   * @param selectedText 选中的文本
   * @param context 上下文
   * @param model 使用的模型
   */
  public async explainText(selectedText: string, context?: string, model?: string): Promise<string> {
    const question = `请解释以下内容：\n\n${selectedText}`;
    return this.askQuestion(question, context, model);
  }

  /**
   * 总结文本内容
   * @param text 要总结的文本
   * @param model 使用的模型
   */
  public async summarizeText(text: string, model?: string): Promise<string> {
    const question = `请总结以下内容的要点：\n\n${text}`;
    return this.askQuestion(question, undefined, model);
  }

  /**
   * 扩展文本内容
   * @param text 要扩展的文本
   * @param context 上下文
   * @param model 使用的模型
   */
  public async expandText(text: string, context?: string, model?: string): Promise<string> {
    const question = `请详细展开以下内容：\n\n${text}`;
    return this.askQuestion(question, context, model);
  }

  /**
   * 翻译文本
   * @param text 要翻译的文本
   * @param targetLanguage 目标语言
   * @param model 使用的模型
   */
  public async translateText(text: string, targetLanguage: string = '中文', model?: string): Promise<string> {
    const question = `请将以下内容翻译成${targetLanguage}：\n\n${text}`;
    return this.askQuestion(question, undefined, model);
  }

  /**
   * 改写文本
   * @param text 要改写的文本
   * @param style 改写风格
   * @param model 使用的模型
   */
  public async rewriteText(text: string, style: string = '更加清晰易懂', model?: string): Promise<string> {
    const question = `请将以下内容改写得${style}：\n\n${text}`;
    return this.askQuestion(question, undefined, model);
  }

  /**
   * 生成思维导图建议
   * @param topic 主题
   * @param model 使用的模型
   */
  public async generateMindMapSuggestions(topic: string, model?: string): Promise<string> {
    const question = `请为主题"${topic}"生成思维导图的分支建议，以Markdown格式返回，使用标题和列表：`;
    return this.askQuestion(question, undefined, model);
  }

  /**
   * 发送HTTP请求到Ollama API
   * @param endpoint API端点
   * @param data 请求数据
   */
  private async sendRequest(endpoint: string, data: any): Promise<OllamaResponse> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.error) {
        throw new Error(result.error);
      }

      return result;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error('请求超时，请检查网络连接或增加超时时间');
      }
      
      throw error;
    }
  }

  /**
   * 设置基础URL
   * @param url 新的基础URL
   */
  public setBaseUrl(url: string): void {
    this.baseUrl = url.replace(/\/$/, '');
  }

  /**
   * 设置默认模型
   * @param model 模型名称
   */
  public setDefaultModel(model: string): void {
    this.defaultModel = model;
  }

  /**
   * 设置请求超时时间
   * @param timeout 超时时间（毫秒）
   */
  public setTimeout(timeout: number): void {
    this.timeout = timeout;
  }

  /**
   * 获取当前配置
   */
  public getConfig(): { baseUrl: string; defaultModel: string; timeout: number } {
    return {
      baseUrl: this.baseUrl,
      defaultModel: this.defaultModel,
      timeout: this.timeout
    };
  }
}

/**
 * AI交互管理器
 * 提供高级的AI交互功能和状态管理
 */
export class AIInteractionManager {
  private aiService: AIService;
  private isProcessing = false;
  private requestQueue: Array<() => Promise<void>> = [];
  private maxConcurrentRequests = 1;
  private activeRequests = 0;

  constructor(aiService: AIService) {
    this.aiService = aiService;
  }

  /**
   * 处理AI问答请求（带队列管理）
   * @param question 问题
   * @param context 上下文
   * @param onProgress 进度回调
   * @param model 模型
   */
  public async processQuestion(
    question: string,
    context?: string,
    onProgress?: (status: string) => void,
    model?: string
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      const task = async () => {
        try {
          this.activeRequests++;
          onProgress?.('正在连接AI服务...');
          
          // 检查连接
          const isConnected = await this.aiService.checkConnection();
          if (!isConnected) {
            throw new Error('无法连接到Ollama服务，请确保服务正在运行');
          }

          onProgress?.('正在处理您的问题...');
          const response = await this.aiService.askQuestion(question, context, model);
          
          onProgress?.('处理完成');
          resolve(response);
        } catch (error) {
          onProgress?.('处理失败');
          reject(error);
        } finally {
          this.activeRequests--;
          this.processQueue();
        }
      };

      if (this.activeRequests < this.maxConcurrentRequests) {
        task();
      } else {
        this.requestQueue.push(task);
        onProgress?.('请求已加入队列，请稍候...');
      }
    });
  }

  /**
   * 处理队列中的请求
   */
  private processQueue(): void {
    if (this.requestQueue.length > 0 && this.activeRequests < this.maxConcurrentRequests) {
      const task = this.requestQueue.shift();
      if (task) {
        task();
      }
    }
  }

  /**
   * 批量处理AI请求
   * @param requests 请求数组
   * @param onProgress 进度回调
   */
  public async processBatch(
    requests: Array<{ question: string; context?: string; model?: string }>,
    onProgress?: (completed: number, total: number) => void
  ): Promise<string[]> {
    const results: string[] = [];
    let completed = 0;

    for (const request of requests) {
      try {
        const result = await this.processQuestion(
          request.question,
          request.context,
          undefined,
          request.model
        );
        results.push(result);
      } catch (error) {
        results.push(`错误: ${error.message}`);
      }
      
      completed++;
      onProgress?.(completed, requests.length);
    }

    return results;
  }

  /**
   * 取消所有待处理的请求
   */
  public cancelPendingRequests(): void {
    this.requestQueue = [];
  }

  /**
   * 获取队列状态
   */
  public getQueueStatus(): { pending: number; active: number } {
    return {
      pending: this.requestQueue.length,
      active: this.activeRequests
    };
  }

  /**
   * 设置最大并发请求数
   * @param max 最大并发数
   */
  public setMaxConcurrentRequests(max: number): void {
    this.maxConcurrentRequests = Math.max(1, max);
  }
}
