/**
 * 双向思维导图插件工具函数
 */

import { v4 as uuidv4 } from 'uuid';
import { ParsedMarkdownNode, MindMapNode, NodeFormat } from './types';

/**
 * 生成唯一ID
 */
export function generateId(): string {
  return uuidv4();
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间（毫秒）
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param limit 限制时间（毫秒）
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * 深拷贝对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
  
  return obj;
}

/**
 * 提取文本中的样式标记
 * @param text 包含样式标记的文本
 * @returns 样式数组和纯文本
 */
export function extractStyles(text: string): { styles: string[]; plainText: string } {
  const styles: string[] = [];
  let plainText = text;

  // 检测粗体
  if (/\*\*.*?\*\*/.test(text)) {
    styles.push('bold');
    plainText = plainText.replace(/\*\*(.*?)\*\*/g, '$1');
  }

  // 检测斜体
  if (/\*.*?\*/.test(text) && !/\*\*.*?\*\*/.test(text)) {
    styles.push('italic');
    plainText = plainText.replace(/\*(.*?)\*/g, '$1');
  }

  // 检测代码
  if (/`.*?`/.test(text)) {
    styles.push('code');
    plainText = plainText.replace(/`(.*?)`/g, '$1');
  }

  // 检测链接
  if (/\[.*?\]\(.*?\)/.test(text)) {
    styles.push('link');
    plainText = plainText.replace(/\[(.*?)\]\(.*?\)/g, '$1');
  }

  return { styles, plainText };
}

/**
 * 将样式数组转换为Markdown格式
 * @param text 纯文本
 * @param styles 样式数组
 * @param url 链接URL（如果有）
 */
export function applyStyles(text: string, styles: string[], url?: string): string {
  let result = text;

  if (styles.includes('bold')) {
    result = `**${result}**`;
  }

  if (styles.includes('italic')) {
    result = `*${result}*`;
  }

  if (styles.includes('code')) {
    result = `\`${result}\``;
  }

  if (styles.includes('link') && url) {
    result = `[${result}](${url})`;
  }

  return result;
}

/**
 * 根据节点类型和层级生成Markdown前缀
 * @param format 节点格式
 */
export function getMarkdownPrefix(format: NodeFormat): string {
  switch (format.type) {
    case 'heading':
      return '#'.repeat(format.level || 1) + ' ';
    case 'ordered-list':
      return '1. ';
    case 'unordered-list':
      return '- ';
    case 'paragraph':
    default:
      return '';
  }
}

/**
 * 计算文本在文档中的位置
 * @param content 文档内容
 * @param searchText 要查找的文本
 * @param startFrom 开始查找的位置
 */
export function findTextPosition(content: string, searchText: string, startFrom: number = 0): { start: number; end: number } | null {
  const index = content.indexOf(searchText, startFrom);
  if (index === -1) {
    return null;
  }
  return {
    start: index,
    end: index + searchText.length
  };
}

/**
 * 验证Ollama服务器连接
 * @param url Ollama服务器地址
 */
export async function validateOllamaConnection(url: string): Promise<boolean> {
  try {
    const response = await fetch(`${url}/api/tags`);
    return response.ok;
  } catch (error) {
    console.error('Ollama connection failed:', error);
    return false;
  }
}

/**
 * 格式化时间戳
 * @param timestamp 时间戳
 */
export function formatTimestamp(timestamp: number): string {
  const date = new Date(timestamp);
  return date.toLocaleString();
}

/**
 * 检查节点是否为根节点
 * @param node 节点
 */
export function isRootNode(node: MindMapNode): boolean {
  return node.parentId === null;
}

/**
 * 获取节点的所有子孙节点ID
 * @param nodeId 节点ID
 * @param allNodes 所有节点的映射
 */
export function getAllDescendantIds(nodeId: string, allNodes: Map<string, MindMapNode>): string[] {
  const result: string[] = [];
  const node = allNodes.get(nodeId);
  
  if (!node) {
    return result;
  }

  for (const childId of node.children) {
    result.push(childId);
    result.push(...getAllDescendantIds(childId, allNodes));
  }

  return result;
}

/**
 * 获取节点的层级深度
 * @param nodeId 节点ID
 * @param allNodes 所有节点的映射
 */
export function getNodeDepth(nodeId: string, allNodes: Map<string, MindMapNode>): number {
  const node = allNodes.get(nodeId);
  if (!node || node.parentId === null) {
    return 0;
  }
  
  return 1 + getNodeDepth(node.parentId, allNodes);
}

/**
 * 安全的JSON解析
 * @param jsonString JSON字符串
 * @param defaultValue 默认值
 */
export function safeJsonParse<T>(jsonString: string, defaultValue: T): T {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.error('JSON parse error:', error);
    return defaultValue;
  }
}

/**
 * 清理HTML标签
 * @param html HTML字符串
 */
export function stripHtml(html: string): string {
  return html.replace(/<[^>]*>/g, '');
}

/**
 * 转义特殊字符
 * @param text 文本
 */
export function escapeHtml(text: string): string {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}
