/**
 * 思维导图节点结构引擎
 * 负责管理思维导图节点的创建、更新、删除和层级调整
 */

import { MindMapNode, NodeFormat, NodePosition, NodeMetadata, ParsedMarkdownNode } from './types';
import { generateId, deepClone, isRootNode, getAllDescendantIds, getNodeDepth } from './utils';

export class MindMapEngine {
  private nodes: Map<string, MindMapNode> = new Map();
  private rootNodeIds: string[] = [];
  private changeListeners: ((event: MindMapChangeEvent) => void)[] = [];

  /**
   * 从Markdown节点创建思维导图节点
   * @param markdownNodes Markdown节点数组
   * @returns 创建的思维导图节点数组
   */
  public createFromMarkdownNodes(markdownNodes: ParsedMarkdownNode[]): MindMapNode[] {
    this.clear();
    const createdNodes: MindMapNode[] = [];

    for (const mdNode of markdownNodes) {
      const mindMapNode = this.convertMarkdownToMindMapNode(mdNode);
      this.addNode(mindMapNode);
      createdNodes.push(mindMapNode);
    }

    this.calculatePositions();
    return createdNodes;
  }

  /**
   * 将Markdown节点转换为思维导图节点
   * @param mdNode Markdown节点
   * @returns 思维导图节点
   */
  private convertMarkdownToMindMapNode(mdNode: ParsedMarkdownNode): MindMapNode {
    const format: NodeFormat = {
      type: mdNode.type,
      level: mdNode.level,
      styles: mdNode.styles
    };

    const position: NodePosition = { x: 0, y: 0 }; // 将在calculatePositions中计算

    const metadata: NodeMetadata = {
      createdAt: Date.now(),
      lastModified: Date.now()
    };

    return {
      id: mdNode.id,
      text: mdNode.content,
      format,
      parentId: mdNode.parentId,
      children: mdNode.children?.map(child => child.id) || [],
      position,
      metadata
    };
  }

  /**
   * 添加节点
   * @param node 要添加的节点
   */
  public addNode(node: MindMapNode): void {
    this.nodes.set(node.id, node);
    
    if (isRootNode(node)) {
      this.rootNodeIds.push(node.id);
    }

    this.notifyChange({
      type: 'node-added',
      nodeId: node.id,
      node: deepClone(node)
    });
  }

  /**
   * 更新节点
   * @param nodeId 节点ID
   * @param updates 更新的属性
   */
  public updateNode(nodeId: string, updates: Partial<MindMapNode>): boolean {
    const node = this.nodes.get(nodeId);
    if (!node) {
      return false;
    }

    const oldNode = deepClone(node);
    
    // 更新属性
    Object.assign(node, updates);
    node.metadata = node.metadata || {};
    node.metadata.lastModified = Date.now();

    this.notifyChange({
      type: 'node-updated',
      nodeId,
      node: deepClone(node),
      oldNode
    });

    return true;
  }

  /**
   * 删除节点及其所有子节点
   * @param nodeId 节点ID
   */
  public deleteNode(nodeId: string): boolean {
    const node = this.nodes.get(nodeId);
    if (!node) {
      return false;
    }

    // 获取所有要删除的节点ID（包括子孙节点）
    const toDelete = [nodeId, ...getAllDescendantIds(nodeId, this.nodes)];
    
    // 从父节点的children中移除
    if (node.parentId) {
      const parent = this.nodes.get(node.parentId);
      if (parent) {
        parent.children = parent.children.filter(id => id !== nodeId);
      }
    } else {
      // 从根节点列表中移除
      this.rootNodeIds = this.rootNodeIds.filter(id => id !== nodeId);
    }

    // 删除所有相关节点
    for (const id of toDelete) {
      this.nodes.delete(id);
    }

    this.notifyChange({
      type: 'node-deleted',
      nodeId,
      deletedNodeIds: toDelete
    });

    return true;
  }

  /**
   * 移动节点到新的父节点下
   * @param nodeId 要移动的节点ID
   * @param newParentId 新父节点ID（null表示移动到根级别）
   * @param insertIndex 插入位置索引（可选）
   */
  public moveNode(nodeId: string, newParentId: string | null, insertIndex?: number): boolean {
    const node = this.nodes.get(nodeId);
    if (!node) {
      return false;
    }

    // 检查是否会形成循环引用
    if (newParentId && this.wouldCreateCycle(nodeId, newParentId)) {
      return false;
    }

    const oldParentId = node.parentId;

    // 从原父节点移除
    if (oldParentId) {
      const oldParent = this.nodes.get(oldParentId);
      if (oldParent) {
        oldParent.children = oldParent.children.filter(id => id !== nodeId);
      }
    } else {
      this.rootNodeIds = this.rootNodeIds.filter(id => id !== nodeId);
    }

    // 添加到新父节点
    node.parentId = newParentId;
    if (newParentId) {
      const newParent = this.nodes.get(newParentId);
      if (newParent) {
        if (insertIndex !== undefined && insertIndex >= 0) {
          newParent.children.splice(insertIndex, 0, nodeId);
        } else {
          newParent.children.push(nodeId);
        }
      }
    } else {
      if (insertIndex !== undefined && insertIndex >= 0) {
        this.rootNodeIds.splice(insertIndex, 0, nodeId);
      } else {
        this.rootNodeIds.push(nodeId);
      }
    }

    // 重新计算位置
    this.calculatePositions();

    this.notifyChange({
      type: 'node-moved',
      nodeId,
      oldParentId,
      newParentId,
      node: deepClone(node)
    });

    return true;
  }

  /**
   * 检查移动是否会创建循环引用
   * @param nodeId 要移动的节点ID
   * @param targetParentId 目标父节点ID
   */
  private wouldCreateCycle(nodeId: string, targetParentId: string): boolean {
    const descendants = getAllDescendantIds(nodeId, this.nodes);
    return descendants.includes(targetParentId);
  }

  /**
   * 获取节点
   * @param nodeId 节点ID
   */
  public getNode(nodeId: string): MindMapNode | undefined {
    return this.nodes.get(nodeId);
  }

  /**
   * 获取所有节点
   */
  public getAllNodes(): MindMapNode[] {
    return Array.from(this.nodes.values());
  }

  /**
   * 获取根节点
   */
  public getRootNodes(): MindMapNode[] {
    return this.rootNodeIds.map(id => this.nodes.get(id)!).filter(Boolean);
  }

  /**
   * 获取节点的子节点
   * @param nodeId 节点ID
   */
  public getChildren(nodeId: string): MindMapNode[] {
    const node = this.nodes.get(nodeId);
    if (!node) {
      return [];
    }
    return node.children.map(id => this.nodes.get(id)!).filter(Boolean);
  }

  /**
   * 获取节点的父节点
   * @param nodeId 节点ID
   */
  public getParent(nodeId: string): MindMapNode | null {
    const node = this.nodes.get(nodeId);
    if (!node || !node.parentId) {
      return null;
    }
    return this.nodes.get(node.parentId) || null;
  }

  /**
   * 计算所有节点的位置
   */
  private calculatePositions(): void {
    const levelSpacing = 200;
    const nodeSpacing = 80;
    
    // 为每个根节点计算位置
    let rootY = 0;
    for (const rootId of this.rootNodeIds) {
      const rootNode = this.nodes.get(rootId);
      if (rootNode) {
        rootNode.position = { x: 0, y: rootY };
        const subtreeHeight = this.calculateSubtreePositions(rootId, levelSpacing, nodeSpacing, rootY);
        rootY += subtreeHeight + nodeSpacing;
      }
    }
  }

  /**
   * 计算子树的位置
   * @param nodeId 节点ID
   * @param levelSpacing 层级间距
   * @param nodeSpacing 节点间距
   * @param startY 起始Y坐标
   * @returns 子树高度
   */
  private calculateSubtreePositions(nodeId: string, levelSpacing: number, nodeSpacing: number, startY: number): number {
    const node = this.nodes.get(nodeId);
    if (!node) {
      return 0;
    }

    const depth = getNodeDepth(nodeId, this.nodes);
    let currentY = startY;

    for (const childId of node.children) {
      const child = this.nodes.get(childId);
      if (child) {
        child.position = { x: (depth + 1) * levelSpacing, y: currentY };
        const childHeight = this.calculateSubtreePositions(childId, levelSpacing, nodeSpacing, currentY);
        currentY += Math.max(nodeSpacing, childHeight);
      }
    }

    return Math.max(nodeSpacing, currentY - startY);
  }

  /**
   * 清空所有节点
   */
  public clear(): void {
    this.nodes.clear();
    this.rootNodeIds = [];
  }

  /**
   * 添加变更监听器
   * @param listener 监听器函数
   */
  public addChangeListener(listener: (event: MindMapChangeEvent) => void): void {
    this.changeListeners.push(listener);
  }

  /**
   * 移除变更监听器
   * @param listener 监听器函数
   */
  public removeChangeListener(listener: (event: MindMapChangeEvent) => void): void {
    const index = this.changeListeners.indexOf(listener);
    if (index > -1) {
      this.changeListeners.splice(index, 1);
    }
  }

  /**
   * 通知变更
   * @param event 变更事件
   */
  private notifyChange(event: MindMapChangeEvent): void {
    for (const listener of this.changeListeners) {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in change listener:', error);
      }
    }
  }
}

/**
 * 思维导图变更事件
 */
export interface MindMapChangeEvent {
  type: 'node-added' | 'node-updated' | 'node-deleted' | 'node-moved';
  nodeId: string;
  node?: MindMapNode;
  oldNode?: MindMapNode;
  oldParentId?: string | null;
  newParentId?: string | null;
  deletedNodeIds?: string[];
}
