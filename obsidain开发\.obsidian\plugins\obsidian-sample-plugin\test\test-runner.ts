/**
 * 简单的测试运行器
 * 用于测试插件的核心功能
 */

import { MarkdownParser } from '../src/markdown-parser';
import { MindMapEngine } from '../src/mindmap-engine';
import { SyncController } from '../src/sync-controller';
import { AIService } from '../src/ai-service';

/**
 * 测试结果接口
 */
interface TestResult {
  name: string;
  passed: boolean;
  error?: string;
  duration: number;
}

/**
 * 测试运行器类
 */
export class TestRunner {
  private results: TestResult[] = [];

  /**
   * 运行所有测试
   */
  public async runAllTests(): Promise<TestResult[]> {
    console.log('开始运行测试...');
    
    await this.testMarkdownParser();
    await this.testMindMapEngine();
    await this.testSyncController();
    await this.testAIService();
    
    this.printResults();
    return this.results;
  }

  /**
   * 测试Markdown解析器
   */
  private async testMarkdownParser(): Promise<void> {
    const parser = new MarkdownParser();

    // 测试基本解析
    await this.runTest('Markdown解析器 - 基本解析', () => {
      const content = `# 标题1\n## 标题2\n这是一段文字\n- 列表项1\n- 列表项2`;
      const result = parser.parseMarkdown(content);
      
      if (result.nodes.length === 0) {
        throw new Error('解析结果为空');
      }
      
      const headings = parser.findNodesByType(result.nodes, 'heading');
      if (headings.length !== 2) {
        throw new Error(`期望2个标题，实际${headings.length}个`);
      }
      
      const lists = parser.findNodesByType(result.nodes, 'unordered-list');
      if (lists.length !== 2) {
        throw new Error(`期望2个列表项，实际${lists.length}个`);
      }
    });

    // 测试样式提取
    await this.runTest('Markdown解析器 - 样式提取', () => {
      const content = `# **粗体标题**\n*斜体文本*\n\`代码文本\``;
      const result = parser.parseMarkdown(content);
      
      const boldNode = result.nodes.find(n => n.styles.includes('bold'));
      if (!boldNode) {
        throw new Error('未找到粗体样式');
      }
      
      const italicNode = result.nodes.find(n => n.styles.includes('italic'));
      if (!italicNode) {
        throw new Error('未找到斜体样式');
      }
      
      const codeNode = result.nodes.find(n => n.styles.includes('code'));
      if (!codeNode) {
        throw new Error('未找到代码样式');
      }
    });

    // 测试双向转换
    await this.runTest('Markdown解析器 - 双向转换', () => {
      const originalContent = `# 标题1\n## 标题2\n这是段落\n- 列表项`;
      const parseResult = parser.parseMarkdown(originalContent);
      const convertedContent = parser.nodesToMarkdown(parseResult.nodes);
      
      // 重新解析转换后的内容
      const secondParseResult = parser.parseMarkdown(convertedContent);
      
      if (parseResult.nodes.length !== secondParseResult.nodes.length) {
        throw new Error('双向转换后节点数量不一致');
      }
    });
  }

  /**
   * 测试思维导图引擎
   */
  private async testMindMapEngine(): Promise<void> {
    const engine = new MindMapEngine();

    // 测试节点创建
    await this.runTest('思维导图引擎 - 节点创建', () => {
      const testNode = {
        id: 'test-1',
        text: '测试节点',
        format: { type: 'heading' as const, level: 1, styles: [] },
        parentId: null,
        children: [],
        position: { x: 0, y: 0 },
        metadata: { createdAt: Date.now() }
      };
      
      engine.addNode(testNode);
      const retrieved = engine.getNode('test-1');
      
      if (!retrieved) {
        throw new Error('节点创建失败');
      }
      
      if (retrieved.text !== '测试节点') {
        throw new Error('节点文本不匹配');
      }
    });

    // 测试节点更新
    await this.runTest('思维导图引擎 - 节点更新', () => {
      const success = engine.updateNode('test-1', { text: '更新后的节点' });
      
      if (!success) {
        throw new Error('节点更新失败');
      }
      
      const updated = engine.getNode('test-1');
      if (!updated || updated.text !== '更新后的节点') {
        throw new Error('节点更新后文本不匹配');
      }
    });

    // 测试节点移动
    await this.runTest('思维导图引擎 - 节点移动', () => {
      // 创建父节点
      const parentNode = {
        id: 'parent-1',
        text: '父节点',
        format: { type: 'heading' as const, level: 1, styles: [] },
        parentId: null,
        children: [],
        position: { x: 0, y: 0 },
        metadata: { createdAt: Date.now() }
      };
      
      engine.addNode(parentNode);
      
      // 移动节点
      const success = engine.moveNode('test-1', 'parent-1');
      
      if (!success) {
        throw new Error('节点移动失败');
      }
      
      const movedNode = engine.getNode('test-1');
      const parent = engine.getNode('parent-1');
      
      if (!movedNode || movedNode.parentId !== 'parent-1') {
        throw new Error('节点父子关系设置失败');
      }
      
      if (!parent || !parent.children.includes('test-1')) {
        throw new Error('父节点子节点列表更新失败');
      }
    });

    // 测试节点删除
    await this.runTest('思维导图引擎 - 节点删除', () => {
      const success = engine.deleteNode('test-1');
      
      if (!success) {
        throw new Error('节点删除失败');
      }
      
      const deleted = engine.getNode('test-1');
      if (deleted) {
        throw new Error('节点删除后仍然存在');
      }
      
      // 检查父节点的子节点列表是否更新
      const parent = engine.getNode('parent-1');
      if (parent && parent.children.includes('test-1')) {
        throw new Error('父节点子节点列表未更新');
      }
    });
  }

  /**
   * 测试同步控制器
   */
  private async testSyncController(): Promise<void> {
    const parser = new MarkdownParser();
    const engine = new MindMapEngine();
    const syncController = new SyncController(parser, engine);

    await this.runTest('同步控制器 - 基本功能', () => {
      // 创建模拟编辑器
      const mockEditor = {
        getValue: () => '# 测试标题\n这是测试内容',
        setValue: (value: string) => {},
        getSelection: () => '测试内容',
        getCursor: () => ({ line: 1, ch: 0 }),
        posToOffset: (pos: any) => 10,
        replaceRange: (text: string, from: any, to?: any) => {},
        on: (event: string, callback: Function) => {},
        off: (event: string, callback: Function) => {}
      };

      const mockView = {} as any;

      // 这里只测试基本的设置功能，因为完整的同步需要真实的编辑器环境
      syncController.setEditor(mockEditor as any, mockView);
      
      // 测试同步映射获取
      const syncMap = syncController.getSyncMap();
      if (!syncMap) {
        throw new Error('同步映射获取失败');
      }
    });
  }

  /**
   * 测试AI服务
   */
  private async testAIService(): Promise<void> {
    const aiService = new AIService();

    await this.runTest('AI服务 - 配置管理', () => {
      const originalConfig = aiService.getConfig();
      
      aiService.setBaseUrl('http://test:11434');
      aiService.setDefaultModel('test-model');
      aiService.setTimeout(60000);
      
      const newConfig = aiService.getConfig();
      
      if (newConfig.baseUrl !== 'http://test:11434') {
        throw new Error('基础URL设置失败');
      }
      
      if (newConfig.defaultModel !== 'test-model') {
        throw new Error('默认模型设置失败');
      }
      
      if (newConfig.timeout !== 60000) {
        throw new Error('超时时间设置失败');
      }
    });

    // 注意：这里不测试实际的网络连接，因为可能没有Ollama服务运行
    await this.runTest('AI服务 - 连接检查（模拟）', () => {
      // 这里只测试方法存在性，不进行实际网络请求
      if (typeof aiService.checkConnection !== 'function') {
        throw new Error('checkConnection方法不存在');
      }
      
      if (typeof aiService.askQuestion !== 'function') {
        throw new Error('askQuestion方法不存在');
      }
      
      if (typeof aiService.explainText !== 'function') {
        throw new Error('explainText方法不存在');
      }
    });
  }

  /**
   * 运行单个测试
   */
  private async runTest(name: string, testFn: () => void | Promise<void>): Promise<void> {
    const startTime = Date.now();
    
    try {
      await testFn();
      const duration = Date.now() - startTime;
      
      this.results.push({
        name,
        passed: true,
        duration
      });
      
      console.log(`✅ ${name} (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.results.push({
        name,
        passed: false,
        error: error.message,
        duration
      });
      
      console.error(`❌ ${name} (${duration}ms): ${error.message}`);
    }
  }

  /**
   * 打印测试结果
   */
  private printResults(): void {
    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    const totalTime = this.results.reduce((sum, r) => sum + r.duration, 0);
    
    console.log('\n=== 测试结果 ===');
    console.log(`通过: ${passed}/${total}`);
    console.log(`总耗时: ${totalTime}ms`);
    
    if (passed === total) {
      console.log('🎉 所有测试通过！');
    } else {
      console.log('❌ 部分测试失败');
      
      const failed = this.results.filter(r => !r.passed);
      failed.forEach(result => {
        console.log(`  - ${result.name}: ${result.error}`);
      });
    }
  }
}
