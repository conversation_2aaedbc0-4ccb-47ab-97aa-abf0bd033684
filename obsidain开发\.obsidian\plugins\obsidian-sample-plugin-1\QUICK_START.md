# 🚀 快速开始指南

## 📦 安装插件

### 方法1：开发者模式安装（推荐）

1. **复制插件文件夹**
   ```
   将整个 obsidian-sample-plugin 文件夹复制到：
   你的库/.obsidian/plugins/
   ```

2. **启用插件**
   - 打开 Obsidian
   - 进入 `设置` → `社区插件`
   - 关闭 `安全模式`
   - 在已安装插件中找到 `Mind Map` 并启用

3. **验证安装**
   - 重启 Obsidian 或按 `Ctrl+Shift+P` → "Reload app without saving"
   - 检查是否出现思维导图相关命令

### 方法2：手动构建安装

```bash
# 进入插件目录
cd obsidian-sample-plugin

# 安装依赖
npm install

# 构建插件
npm run build

# 复制必要文件到 Obsidian 插件目录
# main.js, manifest.json, styles.css
```

---

## 🎯 基础使用

### 1. 创建思维导图

#### 方式一：从现有 Markdown 文件
1. 打开任意 `.md` 文件
2. 按 `Ctrl+M` 或使用命令面板搜索 "切换思维导图/Markdown视图"
3. 思维导图将在右侧分屏显示

#### 方式二：创建新思维导图
1. 按 `Ctrl+Shift+P` 打开命令面板
2. 搜索 "创建新的思维导图"
3. 在新标签页中打开空白思维导图

### 2. 支持的 Markdown 语法

#### 标题结构
```markdown
# 中心主题
## 主要分支 1
### 子主题 1.1
### 子主题 1.2
## 主要分支 2
### 子主题 2.1
```

#### 列表结构
```markdown
# 项目计划
- 需求分析
  - 用户调研
  - 竞品分析
- 设计阶段
  - UI 设计
  - 交互设计
- 开发阶段
  - 前端开发
  - 后端开发
```

### 3. 基本操作

| 操作 | 方法 |
|------|------|
| 切换视图 | `Ctrl+M` |
| 选择节点 | 单击节点 |
| 编辑节点 | 双击节点 |
| 展开/折叠 | 点击节点旁的圆圈 |
| 缩放 | 鼠标滚轮 |
| 拖拽 | 鼠标拖拽 |

---

## 🎨 自定义配置

### 1. 修改快捷键

编辑 `main.ts` 文件：
```typescript
this.addCommand({
    id: 'toggle-mindmap-markdown',
    name: '切换思维导图/Markdown视图',
    hotkeys: [{ modifiers: ['Ctrl', 'Shift'], key: 'M' }], // 修改这里
    callback: () => this.toggleMindMapMarkdown()
});
```

### 2. 自定义样式

编辑 `styles.css` 文件：
```css
/* 修改根节点颜色 */
.markmap-node[data-depth="0"] text {
    fill: #e74c3c !important;
    font-size: 18px !important;
    font-weight: bold !important;
}

/* 修改连接线样式 */
.markmap-link {
    stroke: #3498db !important;
    stroke-width: 2px !important;
}
```

### 3. 调整思维导图配置

在 `main.ts` 中找到 `DEFAULT_MARKMAP_OPTIONS` 并修改：
```typescript
const DEFAULT_MARKMAP_OPTIONS = {
    autoFit: true,
    duration: 300,
    initialExpandLevel: 2,        // 初始展开层级
    spacingVertical: 15,          // 垂直间距
    spacingHorizontal: 100,       // 水平间距
    paddingX: 30,                 // 内边距
};
```

---

## 🔧 开发指南

### 1. 开发环境搭建

```bash
# 克隆或下载项目
git clone [repository-url]
cd obsidian-sample-plugin

# 安装依赖
npm install

# 启动开发模式（自动监听文件变化）
npm run dev
```

### 2. 开发工作流

1. **修改代码**：编辑 `main.ts`、`styles.css` 等文件
2. **自动构建**：开发模式会自动检测文件变化并重新构建
3. **热重载**：在 Obsidian 中按 `Ctrl+Shift+P` → "Reload app without saving"
4. **测试功能**：创建测试文件验证功能

### 3. 调试技巧

#### 控制台调试
```typescript
// 在代码中添加调试信息
console.log('Debug: 当前节点', this.selectedNode);
console.error('Error: 渲染失败', error);
```

#### 浏览器开发者工具
1. 在 Obsidian 中按 `F12` 打开开发者工具
2. 在 Console 面板查看日志输出
3. 在 Sources 面板设置断点调试

#### 常用调试命令
```javascript
// 在浏览器控制台中执行
// 查看插件实例
app.plugins.plugins['obsidian-mindmap-plugin']

// 查看当前思维导图数据
app.plugins.plugins['obsidian-mindmap-plugin'].rootNode
```

---

## 📝 示例文档

### 示例1：项目管理思维导图

创建文件 `project-management.md`：
```markdown
# 项目管理

## 项目规划
### 需求分析
- 用户需求调研
- 功能需求梳理
- 非功能需求定义
### 项目范围
- 核心功能范围
- 边界条件定义
- 排除项说明

## 团队组织
### 开发团队
- 前端开发工程师
- 后端开发工程师
- 测试工程师
### 产品团队
- 产品经理
- UI/UX 设计师
- 数据分析师

## 时间规划
### 第一阶段（1-2周）
- 需求确认
- 技术方案设计
### 第二阶段（3-6周）
- 核心功能开发
- 单元测试
### 第三阶段（7-8周）
- 集成测试
- 用户验收测试
```

按 `Ctrl+M` 即可看到思维导图效果！

### 示例2：学习笔记思维导图

创建文件 `javascript-learning.md`：
```markdown
# JavaScript 学习笔记

## 基础语法
### 变量声明
- var（函数作用域）
- let（块级作用域）
- const（常量）
### 数据类型
- 原始类型
  - Number
  - String
  - Boolean
  - Undefined
  - Null
  - Symbol
- 引用类型
  - Object
  - Array
  - Function

## 高级特性
### 异步编程
- 回调函数
- Promise
- async/await
### 模块系统
- CommonJS
- ES6 Modules
- AMD/UMD

## 框架生态
### 前端框架
- React
- Vue
- Angular
### 构建工具
- Webpack
- Vite
- Rollup
```

---

## ❓ 常见问题

### Q: 思维导图不显示怎么办？
**A:** 
1. 检查控制台是否有错误（按 F12）
2. 确保 Markdown 文件格式正确
3. 重新加载插件：`Ctrl+Shift+P` → "Reload app without saving"

### Q: 快捷键 Ctrl+M 不生效？
**A:**
1. 检查是否与其他插件冲突
2. 确保当前焦点在 Markdown 编辑器中
3. 尝试使用命令面板：`Ctrl+Shift+P` → 搜索 "切换思维导图"

### Q: 思维导图内容不同步？
**A:**
1. 检查文件是否保存
2. 查看控制台是否有文件监听错误
3. 手动刷新：关闭思维导图视图后重新打开

### Q: 如何导出思维导图？
**A:**
目前支持导出为 Markdown 格式：
1. 使用命令面板搜索 "导出思维导图到Markdown"
2. 或者直接复制思维导图对应的 Markdown 内容

### Q: 支持哪些 Markdown 语法？
**A:**
- ✅ 标题（# ## ### #### ##### ######）
- ✅ 无序列表（- * +）
- ❌ 有序列表（暂不支持）
- ❌ 表格（暂不支持）
- ❌ 代码块（暂不支持）

---

## 🆘 获取帮助

- **查看详细文档**：`PROJECT_DOCUMENTATION.md`
- **技术架构**：`ARCHITECTURE.md`
- **问题反馈**：[GitHub Issues]
- **功能建议**：[GitHub Discussions]

---

*快速开始指南 - 最后更新：2025年7月20日*
