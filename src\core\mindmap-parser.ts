import { MindMapNode, NodeStackItem } from '../types';
import { NodeOperations } from '../utils/node-operations';

/**
 * Markdown到思维导图的解析器
 */
export class MindMapParser {
    
    /**
     * 解析Markdown内容为思维导图数据
     */
    static parseMarkdownToMindMap(content: string, title: string = '思维导图'): MindMapNode | null {
        console.log('开始解析Markdown内容:', content);
        
        if (!content || content.trim().length === 0) {
            console.log('内容为空，返回null');
            return null;
        }

        const lines = content.split('\n');
        console.log('分割后的行数:', lines.length);

        let rootNode: MindMapNode | null = null;
        const nodeStack: NodeStackItem[] = [];
        let nodeIdCounter = 1;

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const trimmedLine = line.trim();
            
            console.log(`处理第${i + 1}行: "${line}"`);

            // 跳过空行和特殊行
            if (this.shouldSkipLine(trimmedLine)) {
                console.log('跳过特殊行');
                continue;
            }

            // 解析标题 (# ## ### 等)
            const headingMatch = trimmedLine.match(/^(#{1,6})\s+(.+)$/);
            if (headingMatch) {
                const level = headingMatch[1].length;
                const content = headingMatch[2];

                console.log(`发现标题: 级别${level}, 内容"${content}"`);

                const newNode: MindMapNode = {
                    id: `node_${nodeIdCounter++}`,
                    content: content,
                    children: [],
                    isExpanded: true
                };

                // 处理根节点
                if (!rootNode || level === 1) {
                    if (!rootNode) {
                        rootNode = newNode;
                        nodeStack.push({ node: rootNode, level });
                        console.log('设置根节点:', content);
                        continue;
                    } else if (level === 1) {
                        // 如果已经有根节点，新的一级标题作为根节点的子节点
                        newNode.parent = rootNode;
                        rootNode.children.push(newNode);
                        // 清理栈，只保留根节点
                        nodeStack.splice(1);
                        nodeStack.push({ node: newNode, level });
                        console.log('添加一级子节点:', content);
                        continue;
                    }
                }

                // 处理层级关系
                this.processNodeHierarchy(newNode, level, nodeStack);
            }

            // 解析列表项 (- * +)
            const listMatch = trimmedLine.match(/^[\-\*\+]\s+(.+)$/);
            if (listMatch) {
                const content = listMatch[1];
                console.log(`发现列表项: "${content}"`);

                const newNode: MindMapNode = {
                    id: `node_${nodeIdCounter++}`,
                    content: content,
                    children: [],
                    isExpanded: true
                };

                // 列表项作为当前层级的子节点
                if (nodeStack.length > 0) {
                    const currentParent = nodeStack[nodeStack.length - 1].node;
                    newNode.parent = currentParent;
                    currentParent.children.push(newNode);
                    console.log(`添加列表项"${content}"到父节点"${currentParent.content}"`);
                } else if (rootNode) {
                    newNode.parent = rootNode;
                    rootNode.children.push(newNode);
                    console.log(`添加列表项"${content}"到根节点"${rootNode.content}"`);
                }
            }
        }

        if (rootNode) {
            console.log('解析完成，根节点:', rootNode);
            console.log('根节点子节点数量:', rootNode.children.length);
        } else {
            console.log('解析失败，未找到根节点');
        }

        return rootNode;
    }

    /**
     * 判断是否应该跳过该行
     */
    private static shouldSkipLine(line: string): boolean {
        return (
            line.length === 0 ||
            line.startsWith('>') ||
            line.startsWith('---') ||
            line.startsWith('*由 Mind Map 插件生成*') ||
            line.match(/^\s*$/)
        );
    }

    /**
     * 处理节点层级关系
     */
    private static processNodeHierarchy(newNode: MindMapNode, level: number, nodeStack: NodeStackItem[]): void {
        // 找到合适的父节点
        while (nodeStack.length > 0 && nodeStack[nodeStack.length - 1].level >= level) {
            nodeStack.pop();
        }

        if (nodeStack.length > 0) {
            const parentItem = nodeStack[nodeStack.length - 1];
            newNode.parent = parentItem.node;
            parentItem.node.children.push(newNode);
            console.log(`添加节点"${newNode.content}"到父节点"${parentItem.node.content}"`);
        }

        nodeStack.push({ node: newNode, level });
    }

    /**
     * 将思维导图节点转换为Markdown格式
     */
    static mindmapNodeToMarkdown(node: MindMapNode, level: number = 1): string {
        let markdown = '';
        
        // 添加当前节点
        const prefix = '#'.repeat(level);
        markdown += `${prefix} ${node.content}\n\n`;
        
        // 递归添加子节点
        for (const child of node.children) {
            markdown += this.mindmapNodeToMarkdown(child, level + 1);
        }
        
        return markdown;
    }

    /**
     * 生成思维导图的Markdown导出内容
     */
    static generateMarkdownExport(rootNode: MindMapNode): string {
        const timestamp = new Date().toISOString().split('T')[0];
        let markdown = '';
        
        // 添加元数据
        markdown += `> 思维导图导出 - ${timestamp}\n\n`;
        
        // 添加思维导图内容
        markdown += this.mindmapNodeToMarkdown(rootNode);
        
        // 添加分隔线和署名
        markdown += '\n\n\n---\n\n*由 Mind Map 插件生成*\n';
        
        return markdown;
    }
}
