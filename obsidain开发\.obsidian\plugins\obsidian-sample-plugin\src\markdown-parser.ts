/**
 * Markdown格式解析引擎
 * 负责将Markdown文档解析为结构化数据，支持双向转换
 */

import MarkdownIt from 'markdown-it';
import { ParsedMarkdownNode, ParseResult, SyncMap } from './types';
import { generateId, extractStyles, getMarkdownPrefix } from './utils';

export class MarkdownParser {
  private md: MarkdownIt;

  constructor() {
    this.md = new MarkdownIt({
      html: true,
      linkify: true,
      typographer: true
    });
  }

  /**
   * 解析Markdown文档
   * @param content Markdown文档内容
   * @returns 解析结果
   */
  public parseMarkdown(content: string): ParseResult {
    const lines = content.split('\n');
    const nodes: ParsedMarkdownNode[] = [];
    const syncMap: SyncMap = {
      markdownToMindMap: {},
      mindMapToMarkdown: {}
    };
    const errors: string[] = [];

    let currentIndex = 0;
    let nodeStack: ParsedMarkdownNode[] = []; // 用于维护层级关系

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const lineStart = currentIndex;
      const lineEnd = currentIndex + line.length;

      try {
        const node = this.parseLine(line, lineStart, lineEnd, i);
        if (node) {
          // 建立父子关系
          this.establishHierarchy(node, nodeStack);
          nodes.push(node);

          // 建立映射关系
          syncMap.markdownToMindMap[lineStart] = node.id;
          syncMap.mindMapToMarkdown[node.id] = { start: lineStart, end: lineEnd };
        }
      } catch (error) {
        errors.push(`Line ${i + 1}: ${error.message}`);
      }

      currentIndex = lineEnd + 1; // +1 for newline character
    }

    return { nodes, syncMap, errors };
  }

  /**
   * 解析单行内容
   * @param line 行内容
   * @param startIndex 起始索引
   * @param endIndex 结束索引
   * @param lineNumber 行号
   * @returns 解析的节点或null
   */
  private parseLine(line: string, startIndex: number, endIndex: number, lineNumber: number): ParsedMarkdownNode | null {
    const trimmedLine = line.trim();
    
    // 跳过空行
    if (!trimmedLine) {
      return null;
    }

    const id = generateId();
    let type: ParsedMarkdownNode['type'];
    let level: number | undefined;
    let content = trimmedLine;

    // 解析标题
    const headingMatch = trimmedLine.match(/^(#{1,6})\s+(.+)$/);
    if (headingMatch) {
      type = 'heading';
      level = headingMatch[1].length;
      content = headingMatch[2];
    }
    // 解析有序列表
    else if (trimmedLine.match(/^\d+\.\s+(.+)$/)) {
      type = 'ordered-list';
      content = trimmedLine.replace(/^\d+\.\s+/, '');
    }
    // 解析无序列表
    else if (trimmedLine.match(/^[-*+]\s+(.+)$/)) {
      type = 'unordered-list';
      content = trimmedLine.replace(/^[-*+]\s+/, '');
    }
    // 普通段落
    else {
      type = 'paragraph';
    }

    // 提取样式
    const { styles, plainText } = extractStyles(content);

    return {
      id,
      type,
      level,
      content: plainText,
      styles,
      startIndex,
      endIndex,
      parentId: null, // 将在establishHierarchy中设置
      children: []
    };
  }

  /**
   * 建立节点层级关系
   * @param node 当前节点
   * @param nodeStack 节点栈
   */
  private establishHierarchy(node: ParsedMarkdownNode, nodeStack: ParsedMarkdownNode[]): void {
    if (node.type === 'heading') {
      // 清理栈中层级更深或相等的节点
      while (nodeStack.length > 0) {
        const lastNode = nodeStack[nodeStack.length - 1];
        if (lastNode.type === 'heading' && lastNode.level! >= node.level!) {
          nodeStack.pop();
        } else {
          break;
        }
      }

      // 设置父节点
      if (nodeStack.length > 0) {
        const parent = nodeStack[nodeStack.length - 1];
        node.parentId = parent.id;
        if (!parent.children) {
          parent.children = [];
        }
        parent.children.push(node);
      }

      nodeStack.push(node);
    } else {
      // 非标题节点，找到最近的父节点
      if (nodeStack.length > 0) {
        const parent = nodeStack[nodeStack.length - 1];
        node.parentId = parent.id;
        if (!parent.children) {
          parent.children = [];
        }
        parent.children.push(node);
      }
    }
  }

  /**
   * 将解析的节点树转换回Markdown
   * @param nodes 节点数组
   * @returns Markdown文本
   */
  public nodesToMarkdown(nodes: ParsedMarkdownNode[]): string {
    const lines: string[] = [];

    for (const node of nodes) {
      const line = this.nodeToMarkdownLine(node);
      lines.push(line);
    }

    return lines.join('\n');
  }

  /**
   * 将单个节点转换为Markdown行
   * @param node 节点
   * @returns Markdown行
   */
  private nodeToMarkdownLine(node: ParsedMarkdownNode): string {
    const prefix = getMarkdownPrefix({
      type: node.type,
      level: node.level,
      styles: node.styles
    });

    let content = node.content;

    // 应用样式
    if (node.styles.includes('bold')) {
      content = `**${content}**`;
    }
    if (node.styles.includes('italic')) {
      content = `*${content}*`;
    }
    if (node.styles.includes('code')) {
      content = `\`${content}\``;
    }

    return prefix + content;
  }

  /**
   * 更新节点在文档中的位置索引
   * @param nodes 节点数组
   * @param content 文档内容
   * @returns 更新后的同步映射
   */
  public updatePositionIndices(nodes: ParsedMarkdownNode[], content: string): SyncMap {
    const syncMap: SyncMap = {
      markdownToMindMap: {},
      mindMapToMarkdown: {}
    };

    const lines = content.split('\n');
    let currentIndex = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const lineStart = currentIndex;
      const lineEnd = currentIndex + line.length;

      // 查找匹配的节点
      const matchingNode = this.findMatchingNode(line.trim(), nodes);
      if (matchingNode) {
        // 更新节点位置
        matchingNode.startIndex = lineStart;
        matchingNode.endIndex = lineEnd;

        // 更新映射
        syncMap.markdownToMindMap[lineStart] = matchingNode.id;
        syncMap.mindMapToMarkdown[matchingNode.id] = { start: lineStart, end: lineEnd };
      }

      currentIndex = lineEnd + 1;
    }

    return syncMap;
  }

  /**
   * 查找匹配的节点
   * @param line 行内容
   * @param nodes 节点数组
   * @returns 匹配的节点或null
   */
  private findMatchingNode(line: string, nodes: ParsedMarkdownNode[]): ParsedMarkdownNode | null {
    for (const node of nodes) {
      const expectedLine = this.nodeToMarkdownLine(node);
      if (expectedLine.trim() === line) {
        return node;
      }
    }
    return null;
  }

  /**
   * 验证Markdown语法
   * @param content Markdown内容
   * @returns 验证结果
   */
  public validateMarkdown(content: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    const lines = content.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      // 检查标题格式
      const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);
      if (line.startsWith('#') && !headingMatch) {
        errors.push(`Line ${i + 1}: Invalid heading format`);
      }

      // 检查列表格式
      if (line.match(/^\d+\.\s*$/) || line.match(/^[-*+]\s*$/)) {
        errors.push(`Line ${i + 1}: Empty list item`);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 获取文档统计信息
   * @param content Markdown内容
   * @returns 统计信息
   */
  public getDocumentStats(content: string): {
    totalLines: number;
    headings: number;
    paragraphs: number;
    lists: number;
    words: number;
    characters: number;
  } {
    const lines = content.split('\n');
    let headings = 0;
    let paragraphs = 0;
    let lists = 0;
    let words = 0;
    let characters = content.length;

    for (const line of lines) {
      const trimmed = line.trim();
      if (!trimmed) continue;

      if (trimmed.match(/^#{1,6}\s+/)) {
        headings++;
      } else if (trimmed.match(/^(\d+\.|-|\*|\+)\s+/)) {
        lists++;
      } else {
        paragraphs++;
      }

      words += trimmed.split(/\s+/).filter(word => word.length > 0).length;
    }

    return {
      totalLines: lines.length,
      headings,
      paragraphs,
      lists,
      words,
      characters
    };
  }

  /**
   * 查找特定类型的节点
   * @param nodes 节点数组
   * @param type 节点类型
   * @param level 标题层级（可选）
   * @returns 匹配的节点数组
   */
  public findNodesByType(
    nodes: ParsedMarkdownNode[],
    type: ParsedMarkdownNode['type'],
    level?: number
  ): ParsedMarkdownNode[] {
    const result: ParsedMarkdownNode[] = [];

    const searchNodes = (nodeList: ParsedMarkdownNode[]) => {
      for (const node of nodeList) {
        if (node.type === type && (level === undefined || node.level === level)) {
          result.push(node);
        }
        if (node.children) {
          searchNodes(node.children);
        }
      }
    };

    searchNodes(nodes);
    return result;
  }

  /**
   * 获取节点的完整路径
   * @param nodeId 节点ID
   * @param nodes 所有节点
   * @returns 从根到该节点的路径
   */
  public getNodePath(nodeId: string, nodes: ParsedMarkdownNode[]): ParsedMarkdownNode[] {
    const nodeMap = new Map<string, ParsedMarkdownNode>();

    // 构建节点映射
    const buildMap = (nodeList: ParsedMarkdownNode[]) => {
      for (const node of nodeList) {
        nodeMap.set(node.id, node);
        if (node.children) {
          buildMap(node.children);
        }
      }
    };
    buildMap(nodes);

    const path: ParsedMarkdownNode[] = [];
    let currentNode = nodeMap.get(nodeId);

    while (currentNode) {
      path.unshift(currentNode);
      currentNode = currentNode.parentId ? nodeMap.get(currentNode.parentId) : undefined;
    }

    return path;
  }
}
