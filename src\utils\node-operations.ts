import { MindMapNode } from '../types';

/**
 * 思维导图节点操作工具类
 */
export class NodeOperations {
    
    /**
     * 查找节点
     */
    static findNode(root: MindMapNode, id: string): MindMapNode | null {
        if (root.id === id) return root;
        for (const child of root.children) {
            const found = this.findNode(child, id);
            if (found) return found;
        }
        return null;
    }

    /**
     * 重建节点树（恢复父节点引用）
     */
    static reconstructNode(node: MindMapNode, parent: MindMapNode | undefined = undefined): MindMapNode {
        const newNode: MindMapNode = {
            id: node.id,
            content: node.content,
            children: [],
            parent: parent,
            isSelected: node.isSelected,
            isExpanded: node.isExpanded !== undefined ? node.isExpanded : true
        };

        // 递归重建子节点
        if (node.children && Array.isArray(node.children)) {
            newNode.children = node.children.map(child => 
                this.reconstructNode(child, newNode)
            );
        }

        return newNode;
    }

    /**
     * 创建新节点
     */
    static createNode(content: string, parent?: MindMapNode): MindMapNode {
        return {
            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
            content: content,
            children: [],
            parent: parent,
            isExpanded: true
        };
    }

    /**
     * 添加子节点
     */
    static addChild(parent: MindMapNode, content: string): MindMapNode {
        const newNode = this.createNode(content, parent);
        parent.children.push(newNode);
        parent.isExpanded = true;
        return newNode;
    }

    /**
     * 添加兄弟节点
     */
    static addSibling(node: MindMapNode, content: string): MindMapNode | null {
        if (!node.parent) return null;
        
        const newNode = this.createNode(content, node.parent);
        const index = node.parent.children.indexOf(node);
        node.parent.children.splice(index + 1, 0, newNode);
        
        return newNode;
    }

    /**
     * 删除节点
     */
    static deleteNode(node: MindMapNode): boolean {
        if (!node.parent) return false; // 不能删除根节点
        
        const index = node.parent.children.indexOf(node);
        if (index > -1) {
            node.parent.children.splice(index, 1);
            return true;
        }
        return false;
    }

    /**
     * 获取节点路径（从根到当前节点）
     */
    static getNodePath(node: MindMapNode): MindMapNode[] {
        const path: MindMapNode[] = [];
        let current: MindMapNode | undefined = node;
        
        while (current) {
            path.unshift(current);
            current = current.parent;
        }
        
        return path;
    }

    /**
     * 获取节点深度
     */
    static getNodeDepth(node: MindMapNode): number {
        let depth = 0;
        let current = node.parent;
        
        while (current) {
            depth++;
            current = current.parent;
        }
        
        return depth;
    }

    /**
     * 获取所有叶子节点
     */
    static getLeafNodes(root: MindMapNode): MindMapNode[] {
        const leaves: MindMapNode[] = [];
        
        function traverse(node: MindMapNode) {
            if (node.children.length === 0) {
                leaves.push(node);
            } else {
                node.children.forEach(traverse);
            }
        }
        
        traverse(root);
        return leaves;
    }

    /**
     * 统计节点总数
     */
    static countNodes(root: MindMapNode): number {
        let count = 1; // 包含根节点
        
        function traverse(node: MindMapNode) {
            node.children.forEach(child => {
                count++;
                traverse(child);
            });
        }
        
        traverse(root);
        return count;
    }

    /**
     * 克隆节点（深拷贝）
     */
    static cloneNode(node: MindMapNode, parent?: MindMapNode): MindMapNode {
        const cloned: MindMapNode = {
            id: node.id,
            content: node.content,
            children: [],
            parent: parent,
            isSelected: node.isSelected,
            isExpanded: node.isExpanded
        };

        cloned.children = node.children.map(child => 
            this.cloneNode(child, cloned)
        );

        return cloned;
    }
}
