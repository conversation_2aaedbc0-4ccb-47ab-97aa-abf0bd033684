/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// main.ts
var main_exports = {};
__export(main_exports, {
  default: () => Reloader
});
module.exports = __toCommonJS(main_exports);
var import_obsidian = require("obsidian");
var Reloader = class extends import_obsidian.Plugin {
  async onload() {
    this.addCommand({
      id: `refresh`,
      name: `Refresh plugin list`,
      callback: async () => {
        await this.reloadPlugin(`plugin-reloader`);
      }
    });
    window.setTimeout(() => {
      const plugins = this.app.plugins.plugins;
      for (let name in plugins) {
        try {
          const m = plugins[name].manifest;
          this.addCommand({
            id: m.id,
            name: `Reload ${m.name}`,
            callback: async () => {
              new import_obsidian.Notice(`Reload ${m.name}`);
              await this.reloadPlugin(m.id);
            }
          });
        } catch (e) {
          console.error(e);
        }
      }
    }, 1e3);
  }
  onunload() {
  }
  async reloadPlugin(pluginName) {
    const { plugins } = this.app;
    try {
      await plugins.disablePlugin(pluginName);
      await plugins.enablePlugin(pluginName);
    } catch (e) {
      console.error(e);
    }
  }
};

/* nosourcemap */