import {
	<PERSON><PERSON>,
	Editor,
	<PERSON>down<PERSON>ie<PERSON>,
	<PERSON><PERSON>,
	Notice,
	<PERSON>lugin,
	PluginSettingTab,
	Setting,
	WorkspaceLeaf,
	ItemView,
	<PERSON>u
} from 'obsidian';

import { MindMapPluginSettings } from './src/types';
import { MarkdownParser } from './src/markdown-parser';
import { MindMapEngine } from './src/mindmap-engine';
import { MindMapRenderer } from './src/mindmap-renderer';
import { SyncController } from './src/sync-controller';
import { AIService, AIInteractionManager } from './src/ai-service';

const DEFAULT_SETTINGS: MindMapPluginSettings = {
	ollamaUrl: 'http://localhost:11434',
	defaultModel: 'llama2',
	viewPosition: 'right',
	autoSync: true,
	syncDelay: 300,
	showAiIndicator: true
};

export default class BidirectionalMindMapPlugin extends Plugin {
	settings: MindMapPluginSettings;
	private parser: <PERSON>down<PERSON>arser;
	private engine: MindMapEngine;
	private syncController: SyncController;
	private aiService: AIService;
	private aiManager: AIInteractionManager;
	private mindMapView: MindMapView | null = null;

	async onload() {
		await this.loadSettings();

		// 初始化核心组件
		this.parser = new MarkdownParser();
		this.engine = new MindMapEngine();
		this.syncController = new SyncController(this.parser, this.engine);
		this.aiService = new AIService(this.settings.ollamaUrl, this.settings.defaultModel);
		this.aiManager = new AIInteractionManager(this.aiService);

		// 注册视图类型
		this.registerView(
			VIEW_TYPE_MINDMAP,
			(leaf) => new MindMapView(leaf, this)
		);

		// 添加功能区图标
		const ribbonIconEl = this.addRibbonIcon('brain', '双向思维导图', (evt: MouseEvent) => {
			this.toggleMindMapView();
		});
		ribbonIconEl.addClass('mindmap-ribbon-icon');

		// 添加状态栏
		const statusBarItemEl = this.addStatusBarItem();
		statusBarItemEl.setText('思维导图');
		statusBarItemEl.addClass('mindmap-status-bar');

		// 注册命令
		this.registerCommands();

		// 注册事件监听器
		this.registerEventListeners();

		// 添加设置面板
		this.addSettingTab(new MindMapSettingTab(this.app, this));

		// 初始化右键菜单
		this.registerEditorContextMenu();

		console.log('双向思维导图插件已加载');
	}

	onunload() {
		this.syncController?.destroy();
		this.mindMapView?.destroy();
		console.log('双向思维导图插件已卸载');
	}

	async loadSettings() {
		this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
	}

	async saveSettings() {
		await this.saveData(this.settings);

		// 更新AI服务配置
		if (this.aiService) {
			this.aiService.setBaseUrl(this.settings.ollamaUrl);
			this.aiService.setDefaultModel(this.settings.defaultModel);
		}

		// 更新同步延迟
		if (this.syncController) {
			this.syncController.setSyncDelay(this.settings.syncDelay);
		}
	}

	/**
	 * 注册命令
	 */
	private registerCommands(): void {
		// 切换思维导图视图
		this.addCommand({
			id: 'toggle-mindmap-view',
			name: '切换思维导图视图',
			callback: () => {
				this.toggleMindMapView();
			}
		});

		// AI问答命令
		this.addCommand({
			id: 'ai-ask-question',
			name: 'AI问答',
			editorCallback: (editor: Editor, view: MarkdownView) => {
				this.handleAIQuestion(editor);
			}
		});

		// 解释选中文本
		this.addCommand({
			id: 'ai-explain-text',
			name: 'AI解释选中文本',
			editorCallback: (editor: Editor, view: MarkdownView) => {
				this.handleAIExplain(editor);
			}
		});

		// 总结文档
		this.addCommand({
			id: 'ai-summarize-document',
			name: 'AI总结文档',
			editorCallback: (editor: Editor, view: MarkdownView) => {
				this.handleAISummarize(editor);
			}
		});

		// 生成思维导图建议
		this.addCommand({
			id: 'ai-generate-mindmap',
			name: 'AI生成思维导图建议',
			editorCallback: (editor: Editor, view: MarkdownView) => {
				this.handleAIGenerateMindMap(editor);
			}
		});

		// 居中思维导图
		this.addCommand({
			id: 'center-mindmap',
			name: '居中思维导图',
			callback: () => {
				if (this.mindMapView?.renderer) {
					this.mindMapView.renderer.centerView();
				}
			}
		});
	}

	/**
	 * 注册事件监听器
	 */
	private registerEventListeners(): void {
		// 监听活动文件变化
		this.registerEvent(
			this.app.workspace.on('active-leaf-change', (leaf) => {
				this.handleActiveLeafChange(leaf);
			})
		);

		// 监听文件打开
		this.registerEvent(
			this.app.workspace.on('file-open', (file) => {
				if (file && this.mindMapView) {
					this.updateMindMapForFile();
				}
			})
		);
	}

	/**
	 * 注册编辑器右键菜单
	 */
	private registerEditorContextMenu(): void {
		this.registerEvent(
			this.app.workspace.on('editor-menu', (menu: Menu, editor: Editor, view: MarkdownView) => {
				const selection = editor.getSelection();

				if (selection) {
					menu.addSeparator();

					menu.addItem((item) => {
						item
							.setTitle('AI问答')
							.setIcon('message-circle')
							.onClick(() => {
								this.handleAIQuestion(editor);
							});
					});

					menu.addItem((item) => {
						item
							.setTitle('AI解释')
							.setIcon('help-circle')
							.onClick(() => {
								this.handleAIExplain(editor);
							});
					});

					menu.addItem((item) => {
						item
							.setTitle('AI扩展')
							.setIcon('expand')
							.onClick(() => {
								this.handleAIExpand(editor);
							});
					});
				}

				menu.addSeparator();

				menu.addItem((item) => {
					item
						.setTitle('切换思维导图')
						.setIcon('brain')
						.onClick(() => {
							this.toggleMindMapView();
						});
				});
			})
		);
	}

	/**
	 * 切换思维导图视图
	 */
	private async toggleMindMapView(): Promise<void> {
		const existing = this.app.workspace.getLeavesOfType(VIEW_TYPE_MINDMAP);

		if (existing.length > 0) {
			// 关闭现有视图
			existing[0].detach();
			this.mindMapView = null;
		} else {
			// 打开新视图
			const leaf = this.app.workspace.getRightLeaf(false);
			await leaf.setViewState({
				type: VIEW_TYPE_MINDMAP,
				active: true
			});

			this.mindMapView = leaf.view as MindMapView;
			this.updateMindMapForFile();
		}
	}

	/**
	 * 处理活动叶子变化
	 */
	private handleActiveLeafChange(leaf: WorkspaceLeaf | null): void {
		if (leaf?.view instanceof MarkdownView) {
			const editor = leaf.view.editor;
			const view = leaf.view;

			// 设置同步控制器的编辑器
			this.syncController.setEditor(editor, view);

			// 更新思维导图
			if (this.mindMapView) {
				this.updateMindMapForFile();
			}
		}
	}

	/**
	 * 更新当前文件的思维导图
	 */
	private updateMindMapForFile(): void {
		const activeView = this.app.workspace.getActiveViewOfType(MarkdownView);
		if (activeView && this.mindMapView) {
			const editor = activeView.editor;
			this.syncController.setEditor(editor, activeView);
		}
	}

	/**
	 * 处理AI问答
	 */
	private async handleAIQuestion(editor: Editor): Promise<void> {
		const context = this.syncController.getSelectionContext();
		if (!context) {
			new Notice('请先选择一些文本');
			return;
		}

		const modal = new AIQuestionModal(this.app, async (question: string) => {
			const notice = new Notice('AI正在思考中...', 0);

			try {
				const response = await this.aiManager.processQuestion(
					question,
					context.context,
					(status) => {
						notice.setMessage(status);
					}
				);

				notice.hide();

				// 插入AI回答
				this.syncController.insertAiResponse(context.selectedText, response, context.position);

				new Notice('AI回答已插入');
			} catch (error) {
				notice.hide();
				new Notice(`AI问答失败: ${error.message}`);
			}
		});

		modal.open();
	}

	/**
	 * 处理AI解释
	 */
	private async handleAIExplain(editor: Editor): Promise<void> {
		const context = this.syncController.getSelectionContext();
		if (!context) {
			new Notice('请先选择一些文本');
			return;
		}

		const notice = new Notice('AI正在解释中...', 0);

		try {
			const response = await this.aiService.explainText(context.selectedText, context.context);
			notice.hide();

			this.syncController.insertAiResponse(context.selectedText, response, context.position);
			new Notice('AI解释已插入');
		} catch (error) {
			notice.hide();
			new Notice(`AI解释失败: ${error.message}`);
		}
	}

	/**
	 * 处理AI扩展
	 */
	private async handleAIExpand(editor: Editor): Promise<void> {
		const context = this.syncController.getSelectionContext();
		if (!context) {
			new Notice('请先选择一些文本');
			return;
		}

		const notice = new Notice('AI正在扩展内容...', 0);

		try {
			const response = await this.aiService.expandText(context.selectedText, context.context);
			notice.hide();

			this.syncController.insertAiResponse(context.selectedText, response, context.position);
			new Notice('AI扩展内容已插入');
		} catch (error) {
			notice.hide();
			new Notice(`AI扩展失败: ${error.message}`);
		}
	}

	/**
	 * 处理AI总结
	 */
	private async handleAISummarize(editor: Editor): Promise<void> {
		const content = editor.getValue();
		if (!content.trim()) {
			new Notice('文档内容为空');
			return;
		}

		const notice = new Notice('AI正在总结文档...', 0);

		try {
			const response = await this.aiService.summarizeText(content);
			notice.hide();

			// 在文档开头插入总结
			const summary = `# 文档总结\n\n${response}\n\n---\n\n`;
			editor.replaceRange(summary, { line: 0, ch: 0 });

			new Notice('文档总结已插入');
		} catch (error) {
			notice.hide();
			new Notice(`AI总结失败: ${error.message}`);
		}
	}

	/**
	 * 处理AI生成思维导图
	 */
	private async handleAIGenerateMindMap(editor: Editor): Promise<void> {
		const modal = new AITopicModal(this.app, async (topic: string) => {
			const notice = new Notice('AI正在生成思维导图建议...', 0);

			try {
				const response = await this.aiService.generateMindMapSuggestions(topic);
				notice.hide();

				// 在光标位置插入建议
				const cursor = editor.getCursor();
				const suggestions = `\n\n## ${topic} - AI思维导图建议\n\n${response}\n\n`;
				editor.replaceRange(suggestions, cursor);

				new Notice('思维导图建议已插入');
			} catch (error) {
				notice.hide();
				new Notice(`生成思维导图失败: ${error.message}`);
			}
		});

		modal.open();
	}
}

// 视图类型常量
export const VIEW_TYPE_MINDMAP = 'mindmap-view';

/**
 * 思维导图视图
 */
export class MindMapView extends ItemView {
	public renderer: MindMapRenderer | null = null;
	private plugin: BidirectionalMindMapPlugin;
	private container: HTMLElement;

	constructor(leaf: WorkspaceLeaf, plugin: BidirectionalMindMapPlugin) {
		super(leaf);
		this.plugin = plugin;
	}

	getViewType(): string {
		return VIEW_TYPE_MINDMAP;
	}

	getDisplayText(): string {
		return '思维导图';
	}

	getIcon(): string {
		return 'brain';
	}

	async onOpen(): Promise<void> {
		this.container = this.containerEl.children[1] as HTMLElement;
		this.container.empty();
		this.container.addClass('mindmap-view-container');

		// 创建渲染器
		this.renderer = new MindMapRenderer(
			this.container,
			this.plugin.engine,
			{
				width: this.container.clientWidth || 800,
				height: this.container.clientHeight || 600
			}
		);

		// 绑定事件
		this.setupEventListeners();

		// 初始渲染
		this.renderer.render();
	}

	async onClose(): Promise<void> {
		this.renderer?.destroy();
		this.renderer = null;
	}

	private setupEventListeners(): void {
		if (!this.renderer) return;

		// 节点双击编辑
		this.renderer.addNodeDoubleClickListener((nodeId: string) => {
			this.editNode(nodeId);
		});

		// 节点右键菜单
		this.renderer.addContextMenuListener((nodeId: string, event: MouseEvent) => {
			this.showNodeContextMenu(nodeId, event);
		});

		// 窗口大小变化
		this.registerEvent(
			this.app.workspace.on('resize', () => {
				if (this.renderer) {
					this.renderer.updateConfig({
						width: this.container.clientWidth,
						height: this.container.clientHeight
					});
				}
			})
		);
	}

	private editNode(nodeId: string): void {
		const node = this.plugin.engine.getNode(nodeId);
		if (!node) return;

		const modal = new NodeEditModal(this.app, node.text, (newText: string) => {
			this.plugin.syncController.updateNodeText(nodeId, newText);
		});
		modal.open();
	}

	private showNodeContextMenu(nodeId: string, event: MouseEvent): void {
		const menu = new Menu();
		const node = this.plugin.engine.getNode(nodeId);
		if (!node) return;

		menu.addItem((item) => {
			item
				.setTitle('编辑节点')
				.setIcon('edit')
				.onClick(() => {
					this.editNode(nodeId);
				});
		});

		menu.addItem((item) => {
			item
				.setTitle('删除节点')
				.setIcon('trash')
				.onClick(() => {
					this.plugin.syncController.deleteNode(nodeId);
				});
		});

		menu.addSeparator();

		menu.addItem((item) => {
			item
				.setTitle('AI解释此节点')
				.setIcon('help-circle')
				.onClick(async () => {
					try {
						const response = await this.plugin.aiService.explainText(node.text);
						const newNodeId = this.plugin.syncController.addNode(nodeId, response, 'paragraph');
						// 标记为AI回答
						const newNode = this.plugin.engine.getNode(newNodeId);
						if (newNode) {
							newNode.metadata = { ...newNode.metadata, isAiAnswer: true };
						}
					} catch (error) {
						new Notice(`AI解释失败: ${error.message}`);
					}
				});
		});

		menu.showAtMouseEvent(event);
	}

	public destroy(): void {
		this.renderer?.destroy();
	}
}

/**
 * AI问答模态框
 */
class AIQuestionModal extends Modal {
	private onSubmit: (question: string) => void;

	constructor(app: App, onSubmit: (question: string) => void) {
		super(app);
		this.onSubmit = onSubmit;
	}

	onOpen(): void {
		const { contentEl } = this;
		contentEl.empty();

		contentEl.createEl('h2', { text: 'AI问答' });

		const inputEl = contentEl.createEl('textarea', {
			attr: {
				placeholder: '请输入您的问题...',
				rows: '4',
				style: 'width: 100%; margin: 10px 0; padding: 10px; border: 1px solid var(--border-color); border-radius: 4px;'
			}
		});

		const buttonContainer = contentEl.createDiv({ attr: { style: 'text-align: right; margin-top: 10px;' } });

		const cancelBtn = buttonContainer.createEl('button', { text: '取消' });
		cancelBtn.style.marginRight = '10px';
		cancelBtn.onclick = () => this.close();

		const submitBtn = buttonContainer.createEl('button', { text: '提问', cls: 'mod-cta' });
		submitBtn.onclick = () => {
			const question = inputEl.value.trim();
			if (question) {
				this.onSubmit(question);
				this.close();
			}
		};

		inputEl.focus();
		inputEl.addEventListener('keydown', (e) => {
			if (e.key === 'Enter' && e.ctrlKey) {
				submitBtn.click();
			}
		});
	}

	onClose(): void {
		const { contentEl } = this;
		contentEl.empty();
	}
}

/**
 * AI主题模态框
 */
class AITopicModal extends Modal {
	private onSubmit: (topic: string) => void;

	constructor(app: App, onSubmit: (topic: string) => void) {
		super(app);
		this.onSubmit = onSubmit;
	}

	onOpen(): void {
		const { contentEl } = this;
		contentEl.empty();

		contentEl.createEl('h2', { text: '生成思维导图建议' });

		const inputEl = contentEl.createEl('input', {
			attr: {
				type: 'text',
				placeholder: '请输入主题...',
				style: 'width: 100%; margin: 10px 0; padding: 10px; border: 1px solid var(--border-color); border-radius: 4px;'
			}
		});

		const buttonContainer = contentEl.createDiv({ attr: { style: 'text-align: right; margin-top: 10px;' } });

		const cancelBtn = buttonContainer.createEl('button', { text: '取消' });
		cancelBtn.style.marginRight = '10px';
		cancelBtn.onclick = () => this.close();

		const submitBtn = buttonContainer.createEl('button', { text: '生成', cls: 'mod-cta' });
		submitBtn.onclick = () => {
			const topic = inputEl.value.trim();
			if (topic) {
				this.onSubmit(topic);
				this.close();
			}
		};

		inputEl.focus();
		inputEl.addEventListener('keydown', (e) => {
			if (e.key === 'Enter') {
				submitBtn.click();
			}
		});
	}

	onClose(): void {
		const { contentEl } = this;
		contentEl.empty();
	}
}

/**
 * 节点编辑模态框
 */
class NodeEditModal extends Modal {
	private initialText: string;
	private onSubmit: (text: string) => void;

	constructor(app: App, initialText: string, onSubmit: (text: string) => void) {
		super(app);
		this.initialText = initialText;
		this.onSubmit = onSubmit;
	}

	onOpen(): void {
		const { contentEl } = this;
		contentEl.empty();

		contentEl.createEl('h2', { text: '编辑节点' });

		const inputEl = contentEl.createEl('textarea', {
			attr: {
				rows: '3',
				style: 'width: 100%; margin: 10px 0; padding: 10px; border: 1px solid var(--border-color); border-radius: 4px;'
			}
		});
		inputEl.value = this.initialText;

		const buttonContainer = contentEl.createDiv({ attr: { style: 'text-align: right; margin-top: 10px;' } });

		const cancelBtn = buttonContainer.createEl('button', { text: '取消' });
		cancelBtn.style.marginRight = '10px';
		cancelBtn.onclick = () => this.close();

		const submitBtn = buttonContainer.createEl('button', { text: '保存', cls: 'mod-cta' });
		submitBtn.onclick = () => {
			const text = inputEl.value.trim();
			if (text) {
				this.onSubmit(text);
				this.close();
			}
		};

		inputEl.focus();
		inputEl.select();
	}

	onClose(): void {
		const { contentEl } = this;
		contentEl.empty();
	}
}

/**
 * 设置面板
 */
class MindMapSettingTab extends PluginSettingTab {
	plugin: BidirectionalMindMapPlugin;

	constructor(app: App, plugin: BidirectionalMindMapPlugin) {
		super(app, plugin);
		this.plugin = plugin;
	}

	display(): void {
		const { containerEl } = this;
		containerEl.empty();

		containerEl.createEl('h2', { text: '双向思维导图设置' });

		// Ollama服务器设置
		new Setting(containerEl)
			.setName('Ollama服务器地址')
			.setDesc('本地Ollama服务器的地址')
			.addText(text => text
				.setPlaceholder('http://localhost:11434')
				.setValue(this.plugin.settings.ollamaUrl)
				.onChange(async (value) => {
					this.plugin.settings.ollamaUrl = value;
					await this.plugin.saveSettings();
				}));

		// 默认AI模型
		new Setting(containerEl)
			.setName('默认AI模型')
			.setDesc('用于AI问答的默认模型')
			.addText(text => text
				.setPlaceholder('llama2')
				.setValue(this.plugin.settings.defaultModel)
				.onChange(async (value) => {
					this.plugin.settings.defaultModel = value;
					await this.plugin.saveSettings();
				}));

		// 视图位置
		new Setting(containerEl)
			.setName('思维导图视图位置')
			.setDesc('思维导图视图的默认位置')
			.addDropdown(dropdown => dropdown
				.addOption('right', '右侧')
				.addOption('left', '左侧')
				.addOption('bottom', '底部')
				.setValue(this.plugin.settings.viewPosition)
				.onChange(async (value: 'right' | 'left' | 'bottom') => {
					this.plugin.settings.viewPosition = value;
					await this.plugin.saveSettings();
				}));

		// 自动同步
		new Setting(containerEl)
			.setName('自动同步')
			.setDesc('是否自动同步Markdown和思维导图')
			.addToggle(toggle => toggle
				.setValue(this.plugin.settings.autoSync)
				.onChange(async (value) => {
					this.plugin.settings.autoSync = value;
					await this.plugin.saveSettings();
				}));

		// 同步延迟
		new Setting(containerEl)
			.setName('同步延迟')
			.setDesc('同步延迟时间（毫秒）')
			.addSlider(slider => slider
				.setLimits(100, 2000, 100)
				.setValue(this.plugin.settings.syncDelay)
				.setDynamicTooltip()
				.onChange(async (value) => {
					this.plugin.settings.syncDelay = value;
					await this.plugin.saveSettings();
				}));

		// AI回答标识
		new Setting(containerEl)
			.setName('显示AI回答标识')
			.setDesc('是否在思维导图中显示AI回答的特殊标识')
			.addToggle(toggle => toggle
				.setValue(this.plugin.settings.showAiIndicator)
				.onChange(async (value) => {
					this.plugin.settings.showAiIndicator = value;
					await this.plugin.saveSettings();
				}));

		// 连接测试
		containerEl.createEl('h3', { text: 'AI服务测试' });

		const testContainer = containerEl.createDiv();
		const testButton = testContainer.createEl('button', { text: '测试Ollama连接', cls: 'mod-cta' });
		const testResult = testContainer.createEl('div', { attr: { style: 'margin-top: 10px;' } });

		testButton.onclick = async () => {
			testButton.disabled = true;
			testButton.textContent = '测试中...';
			testResult.empty();

			try {
				const isConnected = await this.plugin.aiService.checkConnection();
				if (isConnected) {
					testResult.createEl('div', {
						text: '✅ 连接成功',
						attr: { style: 'color: var(--color-green);' }
					});

					// 获取可用模型
					const models = await this.plugin.aiService.getAvailableModels();
					if (models.length > 0) {
						testResult.createEl('div', {
							text: `可用模型: ${models.join(', ')}`,
							attr: { style: 'margin-top: 5px; font-size: 0.9em; color: var(--text-muted);' }
						});
					}
				} else {
					testResult.createEl('div', {
						text: '❌ 连接失败，请检查Ollama服务是否运行',
						attr: { style: 'color: var(--color-red);' }
					});
				}
			} catch (error) {
				testResult.createEl('div', {
					text: `❌ 连接错误: ${error.message}`,
					attr: { style: 'color: var(--color-red);' }
				});
			} finally {
				testButton.disabled = false;
				testButton.textContent = '测试Ollama连接';
			}
		};

		// 使用说明
		containerEl.createEl('h3', { text: '使用说明' });
		const helpText = containerEl.createEl('div', { attr: { style: 'font-size: 0.9em; color: var(--text-muted);' } });
		helpText.innerHTML = `
			<p><strong>快捷键：</strong></p>
			<ul>
				<li>Ctrl+M: 切换思维导图视图</li>
				<li>选中文本后右键: 显示AI功能菜单</li>
			</ul>
			<p><strong>AI功能：</strong></p>
			<ul>
				<li>AI问答: 基于选中文本提问</li>
				<li>AI解释: 解释选中的文本内容</li>
				<li>AI扩展: 扩展选中的文本内容</li>
				<li>AI总结: 总结整个文档</li>
			</ul>
			<p><strong>注意：</strong>使用AI功能前请确保Ollama服务正在运行。</p>
		`;
	}
}
