import { App, TFile, Notice } from 'obsidian';
import { MindMapNode } from '../types';
import { MindMapParser } from '../core/mindmap-parser';
import { TIMING_CONFIG } from './constants';

/**
 * 文件同步工具类
 */
export class FileSync {
    
    /**
     * 同步思维导图到源文件
     */
    static async syncToSourceFile(
        app: App, 
        rootNode: MindMapNode, 
        filePath: string,
        onSyncStart?: () => void,
        onSyncComplete?: () => void
    ): Promise<boolean> {
        try {
            console.log('开始同步思维导图到源文件:', filePath);
            
            // 通知同步开始
            if (onSyncStart) {
                onSyncStart();
            }

            // 查找文件
            const file = app.vault.getAbstractFileByPath(filePath);
            if (!file || !(file instanceof TFile)) {
                console.error('文件不存在:', filePath);
                new Notice('源文件不存在');
                return false;
            }

            // 生成Markdown内容
            const markdownContent = MindMapParser.generateMarkdownExport(rootNode);
            console.log('生成的Markdown内容长度:', markdownContent.length);

            // 更新文件内容
            await app.vault.modify(file, markdownContent);
            console.log(`成功同步思维导图到源文件: ${filePath}`);

            // 延迟通知同步完成
            setTimeout(() => {
                if (onSyncComplete) {
                    onSyncComplete();
                }
            }, TIMING_CONFIG.FILE_SYNC_DELAY);

            return true;

        } catch (error) {
            console.error('同步到源文件时出错:', error);
            new Notice('同步失败: ' + error.message);
            return false;
        }
    }

    /**
     * 从源文件读取并解析思维导图数据
     */
    static async loadFromSourceFile(app: App, filePath: string): Promise<MindMapNode | null> {
        try {
            console.log('从源文件加载思维导图:', filePath);

            // 查找文件
            const file = app.vault.getAbstractFileByPath(filePath);
            if (!file || !(file instanceof TFile)) {
                console.error('文件不存在:', filePath);
                return null;
            }

            // 读取文件内容
            const content = await app.vault.read(file);
            console.log('读取文件内容长度:', content.length);

            // 解析为思维导图数据
            const mindMapData = MindMapParser.parseMarkdownToMindMap(content, file.basename);
            
            if (mindMapData) {
                console.log('成功从源文件解析思维导图数据');
                return mindMapData;
            } else {
                console.log('解析思维导图数据失败');
                return null;
            }

        } catch (error) {
            console.error('从源文件加载时出错:', error);
            return null;
        }
    }

    /**
     * 检查文件是否存在
     */
    static fileExists(app: App, filePath: string): boolean {
        const file = app.vault.getAbstractFileByPath(filePath);
        return file instanceof TFile;
    }

    /**
     * 获取文件的最后修改时间
     */
    static getFileModTime(app: App, filePath: string): number | null {
        const file = app.vault.getAbstractFileByPath(filePath);
        if (file instanceof TFile) {
            return file.stat.mtime;
        }
        return null;
    }

    /**
     * 创建文件监听器配置
     */
    static createFileWatcherConfig(filePath: string) {
        return {
            filePath,
            lastModified: Date.now(),
            isActive: true
        };
    }
}
