{"name": "obsidian-sample-plugin", "version": "1.0.0", "description": "This is a sample plugin for Obsidian (https://obsidian.md)", "main": "main.js", "scripts": {"dev": "node esbuild.config.mjs", "build": "tsc -noEmit -skipLibCheck && node esbuild.config.mjs production", "version": "node version-bump.mjs && git add manifest.json versions.json"}, "keywords": [], "author": "", "license": "MIT", "dependencies": {"d3": "^7.8.5", "uuid": "^9.0.0", "markdown-it": "^13.0.1"}, "devDependencies": {"@types/node": "^16.11.6", "@types/d3": "^7.4.0", "@types/uuid": "^9.0.2", "@types/markdown-it": "^13.0.1", "@typescript-eslint/eslint-plugin": "5.29.0", "@typescript-eslint/parser": "5.29.0", "builtin-modules": "3.3.0", "esbuild": "0.17.3", "obsidian": "latest", "tslib": "2.4.0", "typescript": "4.7.4"}}