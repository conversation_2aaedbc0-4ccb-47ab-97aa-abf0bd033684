import { App, Plugin, <PERSON>pace<PERSON>eaf, Notice, <PERSON>downView, TFile } from 'obsidian';
import { MindMapNode } from '../types';
import { NodeOperations } from '../utils/node-operations';
import { FileSync } from '../utils/file-sync';
import { MindMapParser } from './mindmap-parser';
import { MindMapView } from '../views/mindmap-view';
import { MIND_MAP_VIEW_TYPE } from '../utils/constants';

/**
 * 思维导图插件核心类
 */
export class MindMapPlugin extends Plugin {
    private rootNode: MindMapNode | null = null;
    private currentNode: MindMapNode | null = null;
    private editingNode: MindMapNode | null = null;
    private selectedNode: MindMapNode | null = null;

    /**
     * 插件加载
     */
    async onload(): Promise<void> {
        console.log('Loading MindMap plugin');

        // 注册视图
        this.registerView(
            MIND_MAP_VIEW_TYPE,
            (leaf: WorkspaceLeaf) => new MindMapView(leaf, this)
        );

        // 注册命令
        this.registerCommands();

        console.log('MindMap plugin loaded successfully');
    }

    /**
     * 插件卸载
     */
    onunload(): void {
        console.log('Unloading MindMap plugin');
    }

    /**
     * 注册所有命令
     */
    private registerCommands(): void {
        // 创建新思维导图
        this.addCommand({
            id: 'create-mindmap',
            name: '创建新的思维导图',
            callback: () => this.createNewMindMap()
        });

        // 从当前文件创建思维导图预览
        this.addCommand({
            id: 'create-mindmap-preview',
            name: '为当前文件创建思维导图预览',
            hotkeys: [{ modifiers: ['Ctrl'], key: 'm' }],
            callback: () => this.createMindMapPreview()
        });

        // 从Markdown导入
        this.addCommand({
            id: 'import-from-markdown',
            name: '从当前Markdown文件导入思维导图',
            callback: () => this.importFromMarkdown()
        });

        // 导出到Markdown
        this.addCommand({
            id: 'export-to-markdown',
            name: '导出思维导图到Markdown',
            callback: () => this.exportToMarkdown()
        });

        // 节点导航命令
        this.addCommand({
            id: 'select-parent-node',
            name: '选择父节点',
            callback: () => this.selectParentNode()
        });

        this.addCommand({
            id: 'select-first-child',
            name: '选择第一个子节点',
            callback: () => this.selectFirstChild()
        });

        this.addCommand({
            id: 'select-next-sibling',
            name: '选择下一个同级节点',
            callback: () => this.selectNextSibling()
        });

        this.addCommand({
            id: 'select-previous-sibling',
            name: '选择上一个同级节点',
            callback: () => this.selectPreviousSibling()
        });

        // 节点编辑命令
        this.addCommand({
            id: 'edit-selected-node',
            name: '编辑选中的节点',
            callback: () => this.editSelectedNode()
        });

        this.addCommand({
            id: 'add-child-node',
            name: '添加子节点',
            callback: () => this.addChildNode()
        });

        this.addCommand({
            id: 'add-sibling-node',
            name: '添加同级节点',
            callback: () => this.addSiblingNode()
        });

        this.addCommand({
            id: 'delete-selected-node',
            name: '删除选中的节点',
            callback: () => this.deleteSelectedNode()
        });
    }

    /**
     * 创建新的思维导图
     */
    async createNewMindMap(): Promise<void> {
        this.rootNode = NodeOperations.createNode('中心主题');
        
        // 添加一些默认子节点
        const child1 = NodeOperations.addChild(this.rootNode, '子节点1');
        const child2 = NodeOperations.addChild(this.rootNode, '子节点2');

        // 设置当前节点
        this.currentNode = this.rootNode;
        this.selectedNode = this.rootNode;

        // 创建视图
        const leaf = this.app.workspace.getLeaf(true);
        await leaf.setViewState({
            type: MIND_MAP_VIEW_TYPE,
            state: { data: this.rootNode }
        });

        this.app.workspace.revealLeaf(leaf);
        console.log('Created new mind map');
    }

    /**
     * 为当前文件创建思维导图预览
     */
    async createMindMapPreview(): Promise<void> {
        try {
            const activeView = this.app.workspace.getActiveViewOfType(MarkdownView);
            if (!activeView) {
                new Notice('请先打开一个Markdown文件');
                return;
            }

            const currentFile = activeView.file;
            if (!currentFile) {
                new Notice('当前没有打开的文件');
                return;
            }

            // 读取当前文件内容
            const content = await this.app.vault.read(currentFile);
            const mindMapData = MindMapParser.parseMarkdownToMindMap(content, currentFile.basename);

            if (mindMapData) {
                // 设置思维导图数据
                this.rootNode = mindMapData;
                this.selectedNode = this.rootNode;

                // 创建新的分割视图
                const newLeaf = this.app.workspace.getLeaf('split', 'vertical');

                // 在新leaf中打开思维导图视图
                await newLeaf.setViewState({
                    type: MIND_MAP_VIEW_TYPE,
                    state: {
                        data: this.rootNode,
                        sourceFile: currentFile.path
                    }
                });

                this.app.workspace.revealLeaf(newLeaf);

                console.log(`Created mind map preview for: ${currentFile.path}`);
                new Notice(`为 ${currentFile.basename} 创建了思维导图预览`);
            } else {
                console.log('Failed to parse markdown content');
                new Notice('解析Markdown内容失败');
            }

        } catch (error) {
            console.error('Error creating mind map preview:', error);
            new Notice('创建思维导图预览失败');
        }
    }

    /**
     * 从Markdown导入思维导图
     */
    async importFromMarkdown(): Promise<void> {
        try {
            const activeView = this.app.workspace.getActiveViewOfType(MarkdownView);
            if (!activeView) {
                new Notice('请先打开一个Markdown文件');
                return;
            }

            const activeFile = activeView.file;
            if (!activeFile) {
                new Notice('当前没有打开的文件');
                return;
            }

            // 读取文件内容并解析
            const mindMapData = await FileSync.loadFromSourceFile(this.app, activeFile.path);

            if (mindMapData) {
                // 设置为当前的思维导图数据
                this.rootNode = mindMapData;
                this.selectedNode = this.rootNode;

                // 创建新的思维导图视图
                const leaf = this.app.workspace.getLeaf(true);
                await leaf.setViewState({
                    type: MIND_MAP_VIEW_TYPE,
                    state: { data: this.rootNode }
                });

                this.app.workspace.revealLeaf(leaf);

                console.log(`Imported mind map from: ${activeFile.path}`);
                new Notice(`成功从 ${activeFile.basename} 导入思维导图`);
            } else {
                console.log('Failed to parse markdown content');
                new Notice('解析Markdown内容失败');
            }

        } catch (error) {
            console.error('Error importing from markdown:', error);
            new Notice('导入失败: ' + error.message);
        }
    }

    /**
     * 导出思维导图到Markdown
     */
    async exportToMarkdown(): Promise<void> {
        if (!this.rootNode) {
            new Notice('没有可导出的思维导图');
            return;
        }

        try {
            // 生成文件名
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
            const fileName = `思维导图-${timestamp}.md`;

            // 生成Markdown内容
            const markdownContent = MindMapParser.generateMarkdownExport(this.rootNode);

            // 创建文件
            const file = await this.app.vault.create(fileName, markdownContent);
            
            // 打开文件
            const leaf = this.app.workspace.getLeaf(false);
            await leaf.openFile(file);

            new Notice(`思维导图已导出到 ${fileName}`);
            console.log(`Exported mind map to: ${fileName}`);

        } catch (error) {
            console.error('Error exporting to markdown:', error);
            new Notice('导出失败: ' + error.message);
        }
    }

    // 公共方法供视图调用
    getRootNode(): MindMapNode | null {
        return this.rootNode;
    }

    getSelectedNode(): MindMapNode | null {
        return this.selectedNode;
    }

    getCurrentNode(): MindMapNode | null {
        return this.currentNode;
    }

    /**
     * 更新思维导图数据（用于文件监听器）
     */
    updateMindMapData(data: MindMapNode): void {
        console.log('Updating mind map data');
        this.rootNode = NodeOperations.reconstructNode(data);
        this.currentNode = this.rootNode;
        this.selectedNode = this.rootNode;
        console.log('Mind map data updated');
    }

    /**
     * 加载数据
     */
    async loadData(data?: MindMapNode): Promise<any> {
        if (data) {
            this.rootNode = NodeOperations.reconstructNode(data);
            this.currentNode = this.rootNode;
            // 注意：这里不调用渲染，由视图负责
        }
    }

    // ========== 节点导航方法 ==========

    /**
     * 选择父节点
     */
    selectParentNode(): void {
        if (this.selectedNode && this.selectedNode.parent) {
            this.selectNode(this.selectedNode.parent);
        }
    }

    /**
     * 选择第一个子节点
     */
    selectFirstChild(): void {
        if (this.selectedNode && this.selectedNode.children.length > 0) {
            this.selectNode(this.selectedNode.children[0]);
        }
    }

    /**
     * 选择下一个同级节点
     */
    selectNextSibling(): void {
        if (!this.selectedNode || !this.selectedNode.parent) return;

        const siblings = this.selectedNode.parent.children;
        const currentIndex = siblings.indexOf(this.selectedNode);

        if (currentIndex < siblings.length - 1) {
            this.selectNode(siblings[currentIndex + 1]);
        }
    }

    /**
     * 选择上一个同级节点
     */
    selectPreviousSibling(): void {
        if (!this.selectedNode || !this.selectedNode.parent) return;

        const siblings = this.selectedNode.parent.children;
        const currentIndex = siblings.indexOf(this.selectedNode);

        if (currentIndex > 0) {
            this.selectNode(siblings[currentIndex - 1]);
        }
    }

    /**
     * 选择节点
     */
    selectNode(node: MindMapNode): void {
        // 清除之前的选中状态
        if (this.selectedNode) {
            this.selectedNode.isSelected = false;
        }

        // 设置新的选中节点
        this.selectedNode = node;
        this.currentNode = node;
        node.isSelected = true;

        // 通知视图更新高亮
        this.notifyViewsOfSelection(node.id);
    }

    /**
     * 通知视图更新选中状态
     */
    private notifyViewsOfSelection(nodeId: string): void {
        const views = this.app.workspace.getLeavesOfType(MIND_MAP_VIEW_TYPE);
        views.forEach(leaf => {
            const view = leaf.view as MindMapView;
            if (view && view.getRenderer) {
                view.getRenderer().highlightNode(nodeId);
            }
        });
    }

    // ========== 节点编辑方法 ==========

    /**
     * 编辑选中的节点
     */
    editSelectedNode(): void {
        if (this.selectedNode) {
            this.editingNode = this.selectedNode;
            // 这里需要视图配合显示编辑界面
            console.log('开始编辑节点:', this.selectedNode.content);
        }
    }

    /**
     * 添加子节点
     */
    addChildNode(): void {
        if (!this.selectedNode) return;

        const newNode = NodeOperations.addChild(this.selectedNode, '新子节点');
        this.selectNode(newNode);
        this.notifyViewsOfDataChange();
    }

    /**
     * 添加同级节点
     */
    addSiblingNode(): void {
        if (!this.selectedNode) return;

        const newNode = NodeOperations.addSibling(this.selectedNode, '新节点');
        if (newNode) {
            this.selectNode(newNode);
            this.notifyViewsOfDataChange();
        }
    }

    /**
     * 删除选中的节点
     */
    deleteSelectedNode(): void {
        if (!this.selectedNode || this.selectedNode === this.rootNode) {
            new Notice('无法删除根节点');
            return;
        }

        const parent = this.selectedNode.parent;
        const success = NodeOperations.deleteNode(this.selectedNode);

        if (success && parent) {
            this.selectNode(parent);
            this.notifyViewsOfDataChange();
            new Notice('节点已删除');
        }
    }

    /**
     * 通知视图数据已更改
     */
    private notifyViewsOfDataChange(): void {
        const views = this.app.workspace.getLeavesOfType(MIND_MAP_VIEW_TYPE);
        views.forEach(leaf => {
            const view = leaf.view as MindMapView;
            if (view && view.refreshView) {
                view.refreshView();
            }
        });
    }

    // ========== 事件处理方法 ==========

    /**
     * 处理节点点击事件
     */
    handleNodeClick(nodeId: string): void {
        const node = NodeOperations.findNode(this.rootNode!, nodeId);
        if (node) {
            this.selectNode(node);
        }
    }

    /**
     * 处理节点双击事件
     */
    handleNodeDoubleClick(nodeId: string): void {
        const node = NodeOperations.findNode(this.rootNode!, nodeId);
        if (node) {
            this.selectNode(node);
            this.editSelectedNode();
        }
    }

    /**
     * 同步到源文件
     */
    async syncToSourceFile(filePath: string): Promise<void> {
        if (!this.rootNode) {
            new Notice('没有可同步的思维导图数据');
            return;
        }

        const success = await FileSync.syncToSourceFile(
            this.app,
            this.rootNode,
            filePath,
            () => this.disableFileWatchers(filePath),
            () => this.enableFileWatchers(filePath)
        );

        if (success) {
            new Notice('同步成功');
        }
    }

    /**
     * 临时禁用文件监听器
     */
    private disableFileWatchers(filePath: string): void {
        const views = this.app.workspace.getLeavesOfType(MIND_MAP_VIEW_TYPE);
        views.forEach(leaf => {
            const view = leaf.view as MindMapView;
            if (view && view.getSourceFilePath() === filePath) {
                // 这里需要视图提供禁用监听器的方法
                console.log('Temporarily disabling file watcher for:', filePath);
            }
        });
    }

    /**
     * 重新启用文件监听器
     */
    private enableFileWatchers(filePath: string): void {
        const views = this.app.workspace.getLeavesOfType(MIND_MAP_VIEW_TYPE);
        views.forEach(leaf => {
            const view = leaf.view as MindMapView;
            if (view && view.getSourceFilePath() === filePath) {
                // 这里需要视图提供重新启用监听器的方法
                console.log('Re-enabling file watcher for:', filePath);
                view.setupFileWatcher(filePath);
            }
        });
    }
}
