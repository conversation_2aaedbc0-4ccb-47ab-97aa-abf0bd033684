import { ItemView, WorkspaceLeaf, ViewStateResult } from 'obsidian';
import { MindMapNode, MindMapViewState } from '../types';
import { MindMapRenderer } from '../core/mindmap-renderer';
import { FileSync } from '../utils/file-sync';
import { MindMapParser } from '../core/mindmap-parser';
import { MIND_MAP_VIEW_TYPE, TIMING_CONFIG } from '../utils/constants';

/**
 * 思维导图视图类
 */
export class MindMapView extends ItemView {
    private plugin: any; // 主插件实例
    private renderer: MindMapRenderer;
    private sourceFilePath: string | null = null;
    private fileWatcher: any = null;

    constructor(leaf: WorkspaceLeaf, plugin: any) {
        super(leaf);
        this.plugin = plugin;
        this.renderer = new MindMapRenderer();
    }

    getViewType(): string {
        return MIND_MAP_VIEW_TYPE;
    }

    getDisplayText(): string {
        return "思维导图";
    }

    getIcon(): string {
        return "git-branch";
    }

    /**
     * 获取视图状态
     */
    getState(): Record<string, unknown> {
        return {
            type: MIND_MAP_VIEW_TYPE,
            data: this.plugin.getRootNode(),
            sourceFile: this.sourceFilePath
        };
    }

    /**
     * 设置视图状态
     */
    async setState(state: Record<string, unknown>, _result: ViewStateResult): Promise<void> {
        const data = state.data as unknown;
        const sourceFile = state.sourceFile as string;

        if (this.isMindMapNode(data)) {
            await this.plugin.loadData(data);

            // 设置源文件监听
            console.log('setState - sourceFile:', sourceFile, 'current sourceFilePath:', this.sourceFilePath);
            if (sourceFile) {
                if (sourceFile !== this.sourceFilePath) {
                    console.log('Setting up file watcher because source file changed');
                    this.setupFileWatcher(sourceFile);
                } else {
                    console.log('Source file unchanged, keeping existing watcher');
                }
            } else {
                console.log('No source file provided in state');
            }
        }
    }

    /**
     * 设置文件监听器
     */
    setupFileWatcher(filePath: string): void {
        console.log('Setting up file watcher for:', filePath);
        
        // 清除之前的监听器
        if (this.fileWatcher) {
            console.log('Clearing previous file watcher');
            this.app.vault.offref(this.fileWatcher);
        }

        this.sourceFilePath = filePath;

        // 监听文件修改
        this.fileWatcher = this.app.vault.on('modify', async (file) => {
            console.log('File modified event:', file.path, 'watching:', this.sourceFilePath);
            if (file.path === this.sourceFilePath) {
                console.log('Source file modified, updating mind map:', file.path);
                await this.updateFromSourceFile(file);
            }
        });

        console.log('Mind map view linked to source file:', filePath);
        console.log('File watcher active:', !!this.fileWatcher);
    }

    /**
     * 从源文件更新思维导图
     */
    private async updateFromSourceFile(file: any): Promise<void> {
        try {
            console.log('Reading source file content:', file.path);
            const content = await this.app.vault.read(file);
            console.log('File content length:', content.length);
            
            const mindMapData = MindMapParser.parseMarkdownToMindMap(content, file.basename);
            console.log('Parsed mind map data:', mindMapData);

            if (mindMapData) {
                console.log('Updating mind map view with new data');
                
                // 使用public方法更新数据
                this.plugin.updateMindMapData(mindMapData);
                
                // 触发视图重新渲染
                await this.refreshView();
                
                console.log('Mind map view updated from source file successfully');
            } else {
                console.log('Failed to parse mind map data from source file');
            }
        } catch (error) {
            console.error('Error updating mind map from source file:', error);
        }
    }

    /**
     * 刷新视图
     */
    async refreshView(): Promise<void> {
        console.log('Refreshing mind map view');
        const container = this.containerEl.querySelector('.mindmap-container') as HTMLElement;
        if (container && this.plugin.getRootNode()) {
            console.log('Container found, triggering render');
            // 重新渲染思维导图
            await this.renderMindMap();
        } else {
            console.log('Container or root node not found for refresh');
        }
    }

    /**
     * 渲染思维导图
     */
    private async renderMindMap(): Promise<void> {
        const container = this.containerEl.querySelector('.mindmap-container') as HTMLElement;
        const rootNode = this.plugin.getRootNode();
        
        if (!container || !rootNode) {
            console.log('Container or root node not available for rendering');
            return;
        }

        try {
            await this.renderer.renderToContainer(
                container,
                rootNode,
                (nodeId) => this.plugin.handleNodeClick(nodeId),
                (nodeId) => this.plugin.handleNodeDoubleClick(nodeId)
            );
            
            // 高亮选中的节点
            const selectedNode = this.plugin.getSelectedNode();
            if (selectedNode) {
                setTimeout(() => {
                    this.renderer.highlightNode(selectedNode.id);
                }, TIMING_CONFIG.HIGHLIGHT_DELAY);
            }
        } catch (error) {
            console.error('Error rendering mind map:', error);
        }
    }

    /**
     * 检查是否为有效的思维导图节点
     */
    private isMindMapNode(data: unknown): data is MindMapNode {
        if (!data || typeof data !== 'object') return false;
        
        const node = data as Partial<MindMapNode>;
        return (
            typeof node.id === 'string' &&
            typeof node.content === 'string' &&
            Array.isArray(node.children) &&
            node.children.every(child => this.isMindMapNode(child))
        );
    }

    /**
     * 视图打开时的初始化
     */
    async onOpen(): Promise<void> {
        const container = this.containerEl.children[1];
        container.empty();
        const mindmapContainer = container.createDiv('mindmap-container');

        // 设置容器样式，确保有明确的尺寸
        mindmapContainer.style.width = '100%';
        mindmapContainer.style.height = '100%';
        mindmapContainer.style.minHeight = '400px';
        mindmapContainer.style.position = 'relative';
        mindmapContainer.style.overflow = 'hidden';

        console.log('MindMap view opened, container created');

        // 等待容器渲染完成
        setTimeout(async () => {
            // 从状态中加载数据
            const state = this.getState();
            if (state.data && this.isMindMapNode(state.data)) {
                await this.plugin.loadData(state.data);
            } else if (this.plugin.getRootNode()) {
                // 如果插件已有根节点，渲染它
                await this.renderMindMap();
            } else {
                // 创建默认的思维导图
                await this.plugin.createNewMindMap();
            }
        }, 100);
    }

    /**
     * 视图关闭时的清理
     */
    async onClose(): Promise<void> {
        // 清理文件监听器
        if (this.fileWatcher) {
            this.app.vault.offref(this.fileWatcher);
            this.fileWatcher = null;
        }
        this.sourceFilePath = null;
        
        // 销毁渲染器
        this.renderer.destroy();
        
        console.log('MindMap view closed and cleaned up');
    }

    /**
     * 获取渲染器实例
     */
    getRenderer(): MindMapRenderer {
        return this.renderer;
    }

    /**
     * 获取源文件路径
     */
    getSourceFilePath(): string | null {
        return this.sourceFilePath;
    }
}
